graph TB
    %% Color definitions
    classDef aspnetPage fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef djangoView fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef database fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    classDef businessLogic fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef report fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    classDef workflow fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    classDef integration fill:#fce4ec,stroke:#ad1457,stroke-width:2px,color:#000

    %% Main Inventory Module Structure
    subgraph INVENTORY_MODULE["📦 Inventory Module - Material Management System"]
        direction TB
        
        %% Entry Points
        INV_DASHBOARD[("📊 Inventory Dashboard<br/>Entry Point")]
        
        %% ASP.NET Structure
        subgraph ASP_NET["🌐 ASP.NET NewERP Structure"]
            direction TB
            
            %% Main Dashboard
            ASP_MAIN_DASH["Dashboard.aspx<br/>Main Inventory Dashboard"]:::aspnetPage
            
            %% Masters Section
            subgraph MASTERS["📋 Masters Section"]
                direction TB
                MASTERS_DASH["Masters/Dashboard.aspx<br/>Masters Menu"]:::aspnetPage
                ITEM_LOCATION_NEW["ItemLocation_New.aspx<br/>Create Item Location"]:::aspnetPage
                ITEM_LOCATION_EDIT["ItemLocation_Edit.aspx<br/>Edit Item Location"]:::aspnetPage
                ITEM_LOCATION_DELETE["ItemLocation_Delete.aspx<br/>Delete Item Location"]:::aspnetPage
                AUTO_WIS_TIME["AutoWIS_Time_Set.aspx<br/>WIS Time Configuration"]:::aspnetPage
                MASTERS_CONFIG["Masters/Web.config<br/>Role: Inventory_Master"]:::aspnetPage
            end
            
            %% Transactions Section
            subgraph TRANSACTIONS["💼 Transactions Section"]
                direction TB
                TRANS_DASH["Transactions/Dashboard.aspx<br/>Transaction Menu"]:::aspnetPage
                
                %% Inward Transactions
                subgraph INWARD_TRANS["📥 Inward Transactions"]
                    INWARD_DASH["Inward_DashBoard.aspx<br/>Inward Menu"]:::aspnetPage
                    
                    %% GIN (Goods Inward Note)
                    subgraph GIN_SECTION["📋 GIN - Goods Inward Note"]
                        GIN_NEW["GoodsInwardNote_GIN_New.aspx<br/>Create GIN"]:::aspnetPage
                        GIN_NEW_PO["GoodsInwardNote_GIN_New_PO_Details.aspx<br/>GIN PO Details"]:::aspnetPage
                        GIN_EDIT["GoodsInwardNote_GIN_Edit.aspx<br/>Edit GIN"]:::aspnetPage
                        GIN_DELETE["GoodsInwardNote_GIN_Delete.aspx<br/>Delete GIN"]:::aspnetPage
                        GIN_PRINT["GoodsInwardNote_GIN_Print.aspx<br/>Print GIN"]:::aspnetPage
                    end
                    
                    %% GRR (Goods Received Receipt)
                    subgraph GRR_SECTION["📋 GRR - Goods Received Receipt"]
                        GRR_NEW["GoodsReceivedReceipt_GRR_New.aspx<br/>Create GRR"]:::aspnetPage
                        GRR_EDIT["GoodsReceivedReceipt_GRR_Edit.aspx<br/>Edit GRR"]:::aspnetPage
                        GRR_DELETE["GoodsReceivedReceipt_GRR_Delete.aspx<br/>Delete GRR"]:::aspnetPage
                        GRR_PRINT["GoodsReceivedReceipt_GRR_Print.aspx<br/>Print GRR"]:::aspnetPage
                        RECEIVED_RECEIPT_DASH["RecievedReciept_Dashboard.aspx<br/>Receipt Dashboard"]:::aspnetPage
                    end
                    
                    %% Supplier Challan
                    subgraph SUPPLIER_CHALLAN["🚚 Supplier Challan"]
                        SUP_CHALLAN_NEW["SupplierChallan_New.aspx<br/>Create Supplier Challan"]:::aspnetPage
                        SUP_CHALLAN_EDIT["SupplierChallan_Edit.aspx<br/>Edit Supplier Challan"]:::aspnetPage
                        SUP_CHALLAN_DELETE["SupplierChallan_Delete.aspx<br/>Delete Supplier Challan"]:::aspnetPage
                        SUP_CHALLAN_CLEAR["SupplierChallan_Clear.aspx<br/>Clear Supplier Challan"]:::aspnetPage
                        SUP_CHALLAN_PRINT["SupplierChallan_Print.aspx<br/>Print Supplier Challan"]:::aspnetPage
                    end
                end
                
                %% Outward Transactions
                subgraph OUTWARD_TRANS["📤 Outward Transactions"]
                    %% MRS (Material Requisition Slip)
                    subgraph MRS_SECTION["📋 MRS - Material Requisition Slip"]
                        MRS_DASH["MaterialRequisitionSlip_Dashboard.aspx<br/>MRS Dashboard"]:::aspnetPage
                        MRS_NEW["MaterialRequisitionSlip_MRS_New.aspx<br/>Create MRS"]:::aspnetPage
                        MRS_EDIT["MaterialRequisitionSlip_MRS_Edit.aspx<br/>Edit MRS"]:::aspnetPage
                        MRS_DELETE["MaterialRequisitionSlip_MRS_Delete.aspx<br/>Delete MRS"]:::aspnetPage
                        MRS_PRINT["MaterialRequisitionSlip_MRS_Print.aspx<br/>Print MRS"]:::aspnetPage
                    end
                    
                    %% MIN (Material Issue Note)
                    subgraph MIN_SECTION["📋 MIN - Material Issue Note"]
                        MIN_DASH["MaterialIssueNote_Dashboard.aspx<br/>MIN Dashboard"]:::aspnetPage
                        MIN_NEW["MaterialIssueNote_MIN_New.aspx<br/>Create MIN"]:::aspnetPage
                        MIN_EDIT["MaterialIssueNote_MIN_Edit.aspx<br/>Edit MIN"]:::aspnetPage
                        MIN_DELETE["MaterialIssueNote_MIN_Delete.aspx<br/>Delete MIN"]:::aspnetPage
                        MIN_PRINT["MaterialIssueNote_MIN_Print.aspx<br/>Print MIN"]:::aspnetPage
                    end
                    
                    %% MRN (Material Return Note)
                    subgraph MRN_SECTION["📋 MRN - Material Return Note"]
                        MRN_DASH["MaterialReturnNote_Dashboard.aspx<br/>MRN Dashboard"]:::aspnetPage
                        MRN_NEW["MaterialReturnNote_MRN_New.aspx<br/>Create MRN"]:::aspnetPage
                        MRN_EDIT["MaterialReturnNote_MRN_Edit.aspx<br/>Edit MRN"]:::aspnetPage
                        MRN_DELETE["MaterialReturnNote_MRN_Delete.aspx<br/>Delete MRN"]:::aspnetPage
                        MRN_PRINT["MaterialReturnNote_MRN_Print.aspx<br/>Print MRN"]:::aspnetPage
                    end
                    
                    %% Customer Challan
                    subgraph CUSTOMER_CHALLAN["🚚 Customer Challan"]
                        CUST_CHALLAN_NEW["CustomerChallan_New.aspx<br/>Create Customer Challan"]:::aspnetPage
                        CUST_CHALLAN_EDIT["CustomerChallan_Edit.aspx<br/>Edit Customer Challan"]:::aspnetPage
                        CUST_CHALLAN_DELETE["CustomerChallan_Delete.aspx<br/>Delete Customer Challan"]:::aspnetPage
                        CUST_CHALLAN_CLEAR["CustomerChallan_Clear.aspx<br/>Clear Customer Challan"]:::aspnetPage
                        CUST_CHALLAN_PRINT["CustomerChallan_Print.aspx<br/>Print Customer Challan"]:::aspnetPage
                    end
                end
                
                %% Credit & Service Notes
                subgraph CREDIT_SERVICE["💳 Credit & Service Notes"]
                    %% MCN (Material Credit Note)
                    MCN_DASH["MaterialCreditNote_Dashboard.aspx<br/>MCN Dashboard"]:::aspnetPage
                    MCN_NEW["MaterialCreditNote_MCN_New.aspx<br/>Create MCN"]:::aspnetPage
                    MCN_EDIT["MaterialCreditNote_MCN_Edit.aspx<br/>Edit MCN"]:::aspnetPage
                    MCN_DELETE["MaterialCreditNote_MCN_Delete.aspx<br/>Delete MCN"]:::aspnetPage
                    MCN_PRINT["MaterialCreditNote_MCN_Print.aspx<br/>Print MCN"]:::aspnetPage
                    
                    %% Service Notes
                    SERVICE_NOTE_DASH["ServiceNote_Dashboard.aspx<br/>Service Note Dashboard"]:::aspnetPage
                    GSN_NEW["GoodsServiceNote_SN_New.aspx<br/>Create Service Note"]:::aspnetPage
                    GSN_EDIT["GoodsServiceNote_SN_Edit.aspx<br/>Edit Service Note"]:::aspnetPage
                    GSN_DELETE["GoodsServiceNote_SN_Delete.aspx<br/>Delete Service Note"]:::aspnetPage
                    GSN_PRINT["GoodsServiceNote_SN_Print.aspx<br/>Print Service Note"]:::aspnetPage
                end
                
                %% WIS (Work Instruction Sheet)
                subgraph WIS_SECTION["📋 WIS - Work Instruction Sheet"]
                    WIS_ACTUAL_MAT["WIS_ActualRun_Material.aspx<br/>WIS Actual Run Material"]:::aspnetPage
                    WIS_ACTUAL_ASM["WIS_ActualRun_Assembly.aspx<br/>WIS Actual Run Assembly"]:::aspnetPage
                    WIS_DRY_RUN["WIS_Dry_Actual_Run.aspx<br/>WIS Dry Run"]:::aspnetPage
                    WIS_RELEASE["ReleaseWIS.aspx<br/>Release WIS"]:::aspnetPage
                    WIS_VIEW_TRANS["WIS_View_TransWise.aspx<br/>WIS Transaction View"]:::aspnetPage
                    WIS_PRINT["WIS_ActualRun_Print.aspx<br/>WIS Print"]:::aspnetPage
                    WISWONO_PRINT["WISWONO_Print_.aspx<br/>WIS Work Order Print"]:::aspnetPage
                end
                
                %% Stock Management
                subgraph STOCK_MGMT["📊 Stock Management"]
                    CLOSING_STOCK["ClosingStock.aspx<br/>Closing Stock"]:::aspnetPage
                    TOTAL_SHORTAGE["TotalShortage_Print.aspx<br/>Total Shortage Print"]:::aspnetPage
                    TOTAL_ISSUE_SHORT["TotalIssueAndShortage_Print.aspx<br/>Total Issue & Shortage"]:::aspnetPage
                end
            end

            %% Reports Section
            subgraph REPORTS["📊 Reports Section"]
                direction TB
                REP_DASH["Reports/Dashboard.aspx<br/>Reports Menu"]:::aspnetPage

                %% Analysis Reports
                subgraph ANALYSIS_REPORTS["📈 Analysis Reports"]
                    ABC_ANALYSIS["ABCAnalysis.aspx<br/>ABC Analysis"]:::aspnetPage
                    ABC_DETAILS["ABCAnalysis_Details.aspx<br/>ABC Analysis Details"]:::aspnetPage
                    MOVING_NONMOVING["Moving_NonMoving_Items.aspx<br/>Moving/Non-Moving Analysis"]:::aspnetPage
                    MOVING_DETAILS["Moving_NonMoving_Items_Details.aspx<br/>Moving Analysis Details"]:::aspnetPage
                end

                %% Stock Reports
                subgraph STOCK_REPORTS["📋 Stock Reports"]
                    STOCK_LEDGER["StockLedger.aspx<br/>Stock Ledger"]:::aspnetPage
                    STOCK_LEDGER_DETAILS["StockLedger_Details.aspx<br/>Stock Ledger Details"]:::aspnetPage
                    STOCK_LEDGER_PRINT["StockLedger_Print.aspx<br/>Stock Ledger Print"]:::aspnetPage
                    STOCK_STATEMENT["Stock_Statement.aspx<br/>Stock Statement"]:::aspnetPage
                    STOCK_STATEMENT_DETAILS["Stock_Statement_Details.aspx<br/>Stock Statement Details"]:::aspnetPage
                end

                %% Work Order Reports
                subgraph WO_REPORTS["🔧 Work Order Reports"]
                    WO_ISSUE["WorkOrder_Issue.aspx<br/>Work Order Issue"]:::aspnetPage
                    WO_ISSUE_DETAILS["WorkOrder_Issue_Details.aspx<br/>WO Issue Details"]:::aspnetPage
                    WO_SHORTAGE_DETAILS["WorkOrder_Shortage_Details.aspx<br/>WO Shortage Details"]:::aspnetPage
                end

                %% Search & General Reports
                subgraph SEARCH_REPORTS["🔍 Search & General Reports"]
                    SEARCH_REPORT["Search.aspx<br/>General Search"]:::aspnetPage
                    SEARCH_DETAILS["Search_Details.aspx<br/>Search Details"]:::aspnetPage
                    SEARCH_MRN["SearchViewFieldMRN.aspx<br/>Search MRN"]:::aspnetPage
                    SEARCH_MRS["SearchViewFieldMRS.aspx<br/>Search MRS"]:::aspnetPage
                    INWARD_OUTWARD["InwardOutwardRegister.aspx<br/>Inward/Outward Register"]:::aspnetPage
                    GENERAL_REPORT["Report.aspx<br/>General Report"]:::aspnetPage
                end

                %% Crystal Reports
                subgraph CRYSTAL_REPORTS["💎 Crystal Reports"]
                    ABC_RPT["ABCAnalysis.rpt<br/>ABC Crystal Report"]:::report
                    MOVING_RPT["Moving_NonMoving_Items.rpt<br/>Moving Analysis Report"]:::report
                    STOCK_LEDGER_RPT["Stock_Ledger.rpt<br/>Stock Ledger Report"]:::report
                    STOCK_STATEMENT_RPT["Stock_Statement.rpt<br/>Stock Statement Report"]:::report
                    WO_ISSUE_RPT["WorkOrder_Issue.rpt<br/>Work Order Issue Report"]:::report
                    WO_SHORTAGE_RPT["WorkOrder_Shortage.rpt<br/>Work Order Shortage Report"]:::report
                end
            end
        end

        %% Django Cortex Structure
        subgraph DJANGO_CORTEX["🐍 Django Cortex Structure"]
            direction TB

            %% Django Dashboard
            DJANGO_DASH["InventoryDashboardView<br/>Main Dashboard"]:::djangoView

            %% Location Management
            subgraph DJANGO_LOCATION["📍 Location Management"]
                ITEM_LOCATION_LIST["ItemLocationListView<br/>List Locations"]:::djangoView
                ITEM_LOCATION_CREATE["ItemLocationCreateView<br/>Create Location"]:::djangoView
                ITEM_LOCATION_UPDATE["ItemLocationUpdateView<br/>Update Location"]:::djangoView
                ITEM_LOCATION_DELETE["ItemLocationDeleteView<br/>Delete Location"]:::djangoView
                WIS_TIME_CONFIG["WISTimeConfigurationListView<br/>WIS Time Config"]:::djangoView
            end

            %% MRS Management
            subgraph DJANGO_MRS["📋 MRS Management"]
                MRS_LIST["MRSListView<br/>List MRS"]:::djangoView
                MRS_TABLE_LIST["MRSTableListView<br/>MRS Table View"]:::djangoView
                MRS_DETAIL["MRSDetailView<br/>MRS Details"]:::djangoView
                MRS_CREATE["MRSCreateView<br/>Create MRS"]:::djangoView
                MRS_UPDATE["MRSUpdateView<br/>Update MRS"]:::djangoView
                MRS_DELETE["MRSDeleteView<br/>Delete MRS"]:::djangoView
                MRS_APPROVAL["MRSApprovalView<br/>MRS Approval"]:::djangoView
                MRS_LINE_ITEM["MRSLineItemCreateView<br/>MRS Line Items"]:::djangoView
            end

            %% MIN/MRN Management
            subgraph DJANGO_MIN_MRN["📋 MIN/MRN Management"]
                MIN_LIST["MINListView<br/>List MIN"]:::djangoView
                MIN_CREATE["MINCreateView<br/>Create MIN"]:::djangoView
                MIN_UPDATE["MINUpdateView<br/>Update MIN"]:::djangoView
                MIN_DELETE["MINDeleteView<br/>Delete MIN"]:::djangoView
                MRN_LIST["MRNListView<br/>List MRN"]:::djangoView
                MRN_CREATE["MRNCreateView<br/>Create MRN"]:::djangoView
                MRN_UPDATE["MRNUpdateView<br/>Update MRN"]:::djangoView
                MRN_DELETE["MRNDeleteView<br/>Delete MRN"]:::djangoView
            end

            %% GIN/GRR Management
            subgraph DJANGO_GIN_GRR["📋 GIN/GRR Management"]
                GIN_LIST["GINListView<br/>List GIN"]:::djangoView
                GIN_CREATE["GINCreateView<br/>Create GIN"]:::djangoView
                GIN_UPDATE["GINUpdateView<br/>Update GIN"]:::djangoView
                GIN_DELETE["GINDeleteView<br/>Delete GIN"]:::djangoView
                GRR_LIST["GRRListView<br/>List GRR"]:::djangoView
                GRR_CREATE["GRRCreateView<br/>Create GRR"]:::djangoView
                GRR_UPDATE["GRRUpdateView<br/>Update GRR"]:::djangoView
                GRR_DELETE["GRRDeleteView<br/>Delete GRR"]:::djangoView
                INWARD_DASHBOARD["inward_dashboard_view<br/>Inward Dashboard"]:::djangoView
            end

            %% MCN & Service Notes
            subgraph DJANGO_MCN_SERVICE["💳 MCN & Service Notes"]
                MCN_LIST["MCNListView<br/>List MCN"]:::djangoView
                MCN_CREATE["MCNCreateView<br/>Create MCN"]:::djangoView
                MCN_UPDATE["MCNUpdateView<br/>Update MCN"]:::djangoView
                MCN_DELETE["MCNDeleteView<br/>Delete MCN"]:::djangoView
                MCN_APPROVAL["MCNApprovalView<br/>MCN Approval"]:::djangoView
                SERVICE_NOTE_LIST["ServiceNoteListView<br/>List Service Notes"]:::djangoView
                SERVICE_NOTE_CREATE["ServiceNoteCreateView<br/>Create Service Note"]:::djangoView
                SERVICE_NOTE_UPDATE["ServiceNoteUpdateView<br/>Update Service Note"]:::djangoView
                SERVICE_NOTE_DELETE["ServiceNoteDeleteView<br/>Delete Service Note"]:::djangoView
            end

            %% Movement Tracking
            subgraph DJANGO_MOVEMENT["📊 Movement Tracking"]
                MOVEMENT_LIST["InventoryMovementMasterListView<br/>Movement List"]:::djangoView
                MOVEMENT_CREATE["InventoryMovementMasterCreateView<br/>Create Movement"]:::djangoView
                MOVEMENT_UPDATE["InventoryMovementMasterUpdateView<br/>Update Movement"]:::djangoView
                MOVEMENT_DELETE["InventoryMovementMasterDeleteView<br/>Delete Movement"]:::djangoView
                STOCK_LEDGER_LIST["StockLedgerListView<br/>Stock Ledger"]:::djangoView
                INVENTORY_SNAPSHOT["InventorySnapshotListView<br/>Inventory Snapshot"]:::djangoView
                MOVEMENT_DASHBOARD["movement_tracking_dashboard_view<br/>Movement Dashboard"]:::djangoView
            end

            %% Reporting
            subgraph DJANGO_REPORTING["📊 Reporting"]
                REPORTS_DASHBOARD["InventoryReportsDashboardView<br/>Reports Dashboard"]:::djangoView
                ABC_ANALYSIS_VIEW["ABCAnalysisView<br/>ABC Analysis"]:::djangoView
                MOVING_ANALYSIS_VIEW["MovingNonMovingAnalysisView<br/>Moving Analysis"]:::djangoView
                STOCK_STATEMENT_VIEW["StockStatementView<br/>Stock Statement"]:::djangoView
                UNIVERSAL_SEARCH["UniversalSearchView<br/>Universal Search"]:::djangoView
            end
        end
