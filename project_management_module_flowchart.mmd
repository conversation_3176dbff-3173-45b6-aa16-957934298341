graph TB
    %% Color definitions
    classDef aspnetPage fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef djangoView fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef database fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    classDef businessLogic fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef report fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    classDef workflow fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    classDef integration fill:#fce4ec,stroke:#ad1457,stroke-width:2px,color:#000

    %% Main Project Management Module Structure
    subgraph PROJECT_MODULE["🚀 Project Management Module - Project Planning & Execution"]
        direction TB
        
        %% Entry Points
        PROJECT_DASHBOARD[("📊 Project Dashboard<br/>Entry Point")]
        
        %% ASP.NET Structure
        subgraph ASP_NET["🌐 ASP.NET NewERP Structure"]
            direction TB
            
            %% Main Dashboard
            ASP_MAIN_DASH["Dashboard.aspx<br/>Main Project Dashboard"]:::aspnetPage
            
            %% Masters Section
            subgraph MASTERS["📋 Masters Section"]
                direction TB
                MASTERS_DASH["Masters/Dashboard.aspx<br/>Masters Menu"]:::aspnetPage
                
                %% Project Setup
                subgraph PROJECT_SETUP["🎯 Project Setup"]
                    PROJECT_TYPE["Project_Type.aspx<br/>Project Type Master"]:::aspnetPage
                    PROJECT_CATEGORY["Project_Category.aspx<br/>Project Category"]:::aspnetPage
                    PROJECT_STATUS["Project_Status.aspx<br/>Project Status"]:::aspnetPage
                    PROJECT_PRIORITY["Project_Priority.aspx<br/>Project Priority"]:::aspnetPage
                    MILESTONE_TEMPLATE["Milestone_Template.aspx<br/>Milestone Template"]:::aspnetPage
                end
                
                %% Resource Setup
                subgraph RESOURCE_SETUP["👥 Resource Setup"]
                    RESOURCE_TYPE["Resource_Type.aspx<br/>Resource Type"]:::aspnetPage
                    SKILL_MASTER["Skill_Master.aspx<br/>Skill Master"]:::aspnetPage
                    ROLE_MASTER["Role_Master.aspx<br/>Role Master"]:::aspnetPage
                    TEAM_TEMPLATE["Team_Template.aspx<br/>Team Template"]:::aspnetPage
                    EQUIPMENT_MASTER["Equipment_Master.aspx<br/>Equipment Master"]:::aspnetPage
                end
                
                %% Task Setup
                subgraph TASK_SETUP["📋 Task Setup"]
                    TASK_TYPE["Task_Type.aspx<br/>Task Type"]:::aspnetPage
                    TASK_CATEGORY["Task_Category.aspx<br/>Task Category"]:::aspnetPage
                    TASK_TEMPLATE["Task_Template.aspx<br/>Task Template"]:::aspnetPage
                    ACTIVITY_MASTER["Activity_Master.aspx<br/>Activity Master"]:::aspnetPage
                    DEPENDENCY_TYPE["Dependency_Type.aspx<br/>Dependency Type"]:::aspnetPage
                end
                
                MASTERS_CONFIG["Masters/Web.config<br/>Role: Project_Master"]:::aspnetPage
            end
            
            %% Transactions Section
            subgraph TRANSACTIONS["💼 Transactions Section"]
                direction TB
                TRANS_DASH["Transactions/Dashboard.aspx<br/>Transaction Menu"]:::aspnetPage
                
                %% Project Management
                subgraph PROJECT_MGMT["🎯 Project Management"]
                    PROJECT_NEW["Project_New.aspx<br/>Create Project"]:::aspnetPage
                    PROJECT_EDIT["Project_Edit.aspx<br/>Edit Project"]:::aspnetPage
                    PROJECT_DELETE["Project_Delete.aspx<br/>Delete Project"]:::aspnetPage
                    PROJECT_APPROVAL["Project_Approval.aspx<br/>Project Approval"]:::aspnetPage
                    PROJECT_CLOSURE["Project_Closure.aspx<br/>Project Closure"]:::aspnetPage
                    PROJECT_HANDOVER["Project_Handover.aspx<br/>Project Handover"]:::aspnetPage
                end
                
                %% Task Management
                subgraph TASK_MGMT["📋 Task Management"]
                    TASK_NEW["Task_New.aspx<br/>Create Task"]:::aspnetPage
                    TASK_EDIT["Task_Edit.aspx<br/>Edit Task"]:::aspnetPage
                    TASK_DELETE["Task_Delete.aspx<br/>Delete Task"]:::aspnetPage
                    TASK_ASSIGNMENT["Task_Assignment.aspx<br/>Task Assignment"]:::aspnetPage
                    TASK_DEPENDENCY["Task_Dependency.aspx<br/>Task Dependency"]:::aspnetPage
                    TASK_PROGRESS["Task_Progress.aspx<br/>Task Progress"]:::aspnetPage
                    TASK_COMPLETION["Task_Completion.aspx<br/>Task Completion"]:::aspnetPage
                end
                
                %% Resource Management
                subgraph RESOURCE_MGMT["👥 Resource Management"]
                    RESOURCE_ALLOCATION["Resource_Allocation.aspx<br/>Resource Allocation"]:::aspnetPage
                    RESOURCE_PLANNING["Resource_Planning.aspx<br/>Resource Planning"]:::aspnetPage
                    RESOURCE_UTILIZATION["Resource_Utilization.aspx<br/>Resource Utilization"]:::aspnetPage
                    TEAM_FORMATION["Team_Formation.aspx<br/>Team Formation"]:::aspnetPage
                    SKILL_MAPPING["Skill_Mapping.aspx<br/>Skill Mapping"]:::aspnetPage
                    WORKLOAD_BALANCING["Workload_Balancing.aspx<br/>Workload Balancing"]:::aspnetPage
                end
                
                %% Time Management
                subgraph TIME_MGMT["⏰ Time Management"]
                    TIME_TRACKING["Time_Tracking.aspx<br/>Time Tracking"]:::aspnetPage
                    TIMESHEET_ENTRY["Timesheet_Entry.aspx<br/>Timesheet Entry"]:::aspnetPage
                    TIMESHEET_APPROVAL["Timesheet_Approval.aspx<br/>Timesheet Approval"]:::aspnetPage
                    MILESTONE_TRACKING["Milestone_Tracking.aspx<br/>Milestone Tracking"]:::aspnetPage
                    DEADLINE_MANAGEMENT["Deadline_Management.aspx<br/>Deadline Management"]:::aspnetPage
                end
                
                %% Budget Management
                subgraph BUDGET_MGMT["💰 Budget Management"]
                    PROJECT_BUDGET["Project_Budget.aspx<br/>Project Budget"]:::aspnetPage
                    BUDGET_ALLOCATION["Budget_Allocation.aspx<br/>Budget Allocation"]:::aspnetPage
                    COST_TRACKING["Cost_Tracking.aspx<br/>Cost Tracking"]:::aspnetPage
                    EXPENSE_ENTRY["Expense_Entry.aspx<br/>Expense Entry"]:::aspnetPage
                    BUDGET_VARIANCE["Budget_Variance.aspx<br/>Budget Variance"]:::aspnetPage
                    COST_APPROVAL["Cost_Approval.aspx<br/>Cost Approval"]:::aspnetPage
                end
                
                %% Quality Management
                subgraph QUALITY_MGMT["✅ Quality Management"]
                    QUALITY_PLAN["Quality_Plan.aspx<br/>Quality Plan"]:::aspnetPage
                    QUALITY_CHECKLIST["Quality_Checklist.aspx<br/>Quality Checklist"]:::aspnetPage
                    QUALITY_REVIEW["Quality_Review.aspx<br/>Quality Review"]:::aspnetPage
                    DEFECT_TRACKING["Defect_Tracking.aspx<br/>Defect Tracking"]:::aspnetPage
                    QUALITY_AUDIT["Quality_Audit.aspx<br/>Quality Audit"]:::aspnetPage
                end
                
                %% Risk Management
                subgraph RISK_MGMT["⚠️ Risk Management"]
                    RISK_IDENTIFICATION["Risk_Identification.aspx<br/>Risk Identification"]:::aspnetPage
                    RISK_ASSESSMENT["Risk_Assessment.aspx<br/>Risk Assessment"]:::aspnetPage
                    RISK_MITIGATION["Risk_Mitigation.aspx<br/>Risk Mitigation"]:::aspnetPage
                    RISK_MONITORING["Risk_Monitoring.aspx<br/>Risk Monitoring"]:::aspnetPage
                    ISSUE_TRACKING["Issue_Tracking.aspx<br/>Issue Tracking"]:::aspnetPage
                end
                
                %% Communication Management
                subgraph COMMUNICATION_MGMT["📢 Communication Management"]
                    PROJECT_COMMUNICATION["Project_Communication.aspx<br/>Project Communication"]:::aspnetPage
                    MEETING_MANAGEMENT["Meeting_Management.aspx<br/>Meeting Management"]:::aspnetPage
                    DOCUMENT_SHARING["Document_Sharing.aspx<br/>Document Sharing"]:::aspnetPage
                    NOTIFICATION_CENTER["Notification_Center.aspx<br/>Notification Center"]:::aspnetPage
                    STATUS_REPORTING["Status_Reporting.aspx<br/>Status Reporting"]:::aspnetPage
                end
            end
            
            %% Reports Section
            subgraph REPORTS["📊 Reports Section"]
                direction TB
                REP_DASH["Reports/Dashboard.aspx<br/>Reports Menu"]:::aspnetPage
                
                %% Project Reports
                subgraph PROJECT_REPORTS["🎯 Project Reports"]
                    PROJECT_STATUS_REPORT["Project_Status_Report.aspx<br/>Project Status"]:::aspnetPage
                    PROJECT_PROGRESS_REPORT["Project_Progress_Report.aspx<br/>Project Progress"]:::aspnetPage
                    PROJECT_SUMMARY["Project_Summary.aspx<br/>Project Summary"]:::aspnetPage
                    MILESTONE_REPORT["Milestone_Report.aspx<br/>Milestone Report"]:::aspnetPage
                    PROJECT_COMPARISON["Project_Comparison.aspx<br/>Project Comparison"]:::aspnetPage
                end
                
                %% Resource Reports
                subgraph RESOURCE_REPORTS["👥 Resource Reports"]
                    RESOURCE_UTILIZATION_REPORT["Resource_Utilization_Report.aspx<br/>Resource Utilization"]:::aspnetPage
                    TEAM_PERFORMANCE["Team_Performance.aspx<br/>Team Performance"]:::aspnetPage
                    SKILL_ANALYSIS["Skill_Analysis.aspx<br/>Skill Analysis"]:::aspnetPage
                    WORKLOAD_REPORT["Workload_Report.aspx<br/>Workload Report"]:::aspnetPage
                end
                
                %% Time Reports
                subgraph TIME_REPORTS["⏰ Time Reports"]
                    TIMESHEET_REPORT["Timesheet_Report.aspx<br/>Timesheet Report"]:::aspnetPage
                    TIME_ANALYSIS["Time_Analysis.aspx<br/>Time Analysis"]:::aspnetPage
                    PRODUCTIVITY_REPORT["Productivity_Report.aspx<br/>Productivity Report"]:::aspnetPage
                    OVERTIME_ANALYSIS["Overtime_Analysis.aspx<br/>Overtime Analysis"]:::aspnetPage
                end
                
                %% Budget Reports
                subgraph BUDGET_REPORTS["💰 Budget Reports"]
                    BUDGET_VARIANCE_REPORT["Budget_Variance_Report.aspx<br/>Budget Variance"]:::aspnetPage
                    COST_ANALYSIS["Cost_Analysis.aspx<br/>Cost Analysis"]:::aspnetPage
                    EXPENSE_REPORT["Expense_Report.aspx<br/>Expense Report"]:::aspnetPage
                    PROFITABILITY_ANALYSIS["Profitability_Analysis.aspx<br/>Profitability Analysis"]:::aspnetPage
                end
                
                %% Crystal Reports
                subgraph CRYSTAL_REPORTS["💎 Crystal Reports"]
                    PROJECT_STATUS_RPT["Project_Status.rpt<br/>Project Status Report"]:::report
                    GANTT_CHART_RPT["Gantt_Chart.rpt<br/>Gantt Chart Report"]:::report
                    RESOURCE_ALLOCATION_RPT["Resource_Allocation.rpt<br/>Resource Allocation"]:::report
                    TIMESHEET_RPT["Timesheet.rpt<br/>Timesheet Report"]:::report
                    BUDGET_SUMMARY_RPT["Budget_Summary.rpt<br/>Budget Summary"]:::report
                end
            end
        end
        
        %% Django Cortex Structure
        subgraph DJANGO_CORTEX["🐍 Django Cortex Structure"]
            direction TB
            
            %% Django Dashboard
            DJANGO_DASH["ProjectDashboardView<br/>Main Dashboard"]:::djangoView
            
            %% Project Management
            subgraph DJANGO_PROJECT["🎯 Project Management"]
                PROJECT_LIST["ProjectListView<br/>Project List"]:::djangoView
                PROJECT_CREATE["ProjectCreateView<br/>Create Project"]:::djangoView
                PROJECT_UPDATE["ProjectUpdateView<br/>Update Project"]:::djangoView
                PROJECT_DELETE["ProjectDeleteView<br/>Delete Project"]:::djangoView
                PROJECT_DETAIL["ProjectDetailView<br/>Project Details"]:::djangoView
                PROJECT_KANBAN["ProjectKanbanView<br/>Kanban Board"]:::djangoView
            end
            
            %% Task Management
            subgraph DJANGO_TASK["📋 Task Management"]
                TASK_LIST["TaskListView<br/>Task List"]:::djangoView
                TASK_CREATE["TaskCreateView<br/>Create Task"]:::djangoView
                TASK_UPDATE["TaskUpdateView<br/>Update Task"]:::djangoView
                TASK_DELETE["TaskDeleteView<br/>Delete Task"]:::djangoView
                TASK_BOARD["TaskBoardView<br/>Task Board"]:::djangoView
                TASK_CALENDAR["TaskCalendarView<br/>Task Calendar"]:::djangoView
            end
            
            %% Resource Management
            subgraph DJANGO_RESOURCE["👥 Resource Management"]
                RESOURCE_LIST["ResourceListView<br/>Resource List"]:::djangoView
                RESOURCE_ALLOCATION_VIEW["ResourceAllocationView<br/>Resource Allocation"]:::djangoView
                TEAM_LIST["TeamListView<br/>Team List"]:::djangoView
                TEAM_CREATE["TeamCreateView<br/>Create Team"]:::djangoView
                SKILL_MATRIX["SkillMatrixView<br/>Skill Matrix"]:::djangoView
            end
            
            %% Time Management
            subgraph DJANGO_TIME["⏰ Time Management"]
                TIMESHEET_LIST["TimesheetListView<br/>Timesheet List"]:::djangoView
                TIMESHEET_CREATE["TimesheetCreateView<br/>Create Timesheet"]:::djangoView
                TIME_TRACKING_VIEW["TimeTrackingView<br/>Time Tracking"]:::djangoView
                MILESTONE_LIST["MilestoneListView<br/>Milestone List"]:::djangoView
            end
            
            %% Reporting
            subgraph DJANGO_REPORTING["📊 Project Reporting"]
                PROJECT_REPORTS_DASH["ProjectReportsDashboardView<br/>Reports Dashboard"]:::djangoView
                PROJECT_ANALYTICS["ProjectAnalyticsView<br/>Project Analytics"]:::djangoView
                RESOURCE_ANALYTICS["ResourceAnalyticsView<br/>Resource Analytics"]:::djangoView
                TIME_ANALYTICS["TimeAnalyticsView<br/>Time Analytics"]:::djangoView
                BUDGET_ANALYTICS["BudgetAnalyticsView<br/>Budget Analytics"]:::djangoView
            end
        end
