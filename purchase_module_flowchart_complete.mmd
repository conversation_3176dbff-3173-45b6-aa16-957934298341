graph TB
    %% Color definitions
    classDef aspnetPage fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef djangoView fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef database fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    classDef businessLogic fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef report fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    classDef workflow fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    classDef integration fill:#fce4ec,stroke:#ad1457,stroke-width:2px,color:#000

    %% Main Purchase Module Structure
    subgraph PURCHASE_MODULE["🛒 Purchase Module - Procurement & Vendor Management System"]
        direction TB
        
        %% Entry Points
        PURCHASE_DASHBOARD[("📊 Purchase Dashboard<br/>Entry Point")]
        
        %% ASP.NET Structure
        subgraph ASP_NET["🌐 ASP.NET NewERP Structure"]
            direction TB
            
            %% Main Dashboard
            ASP_MAIN_DASH["Dashboard.aspx<br/>Main Purchase Dashboard"]:::aspnetPage
            
            %% Masters Section
            subgraph MASTERS["📋 Masters Section"]
                direction TB
                MASTERS_DASH["Masters/Dashboard.aspx<br/>Masters Menu"]:::aspnetPage
                
                %% Supplier Management
                subgraph SUPPLIER_MGMT["👥 Supplier Management"]
                    SUPPLIER_MASTER["Supplier_Master.aspx<br/>Supplier Master"]:::aspnetPage
                    SUPPLIER_CATEGORY["Supplier_Category.aspx<br/>Supplier Category"]:::aspnetPage
                    SUPPLIER_GROUP["Supplier_Group.aspx<br/>Supplier Group"]:::aspnetPage
                    SUPPLIER_CONTACT["Supplier_Contact.aspx<br/>Supplier Contact"]:::aspnetPage
                    SUPPLIER_RATING["Supplier_Rating.aspx<br/>Supplier Rating"]:::aspnetPage
                    SUPPLIER_BLACKLIST["Supplier_Blacklist.aspx<br/>Supplier Blacklist"]:::aspnetPage
                    VENDOR_EVALUATION["Vendor_Evaluation.aspx<br/>Vendor Evaluation"]:::aspnetPage
                end
                
                %% Item Management
                subgraph ITEM_MGMT["📦 Item Management"]
                    ITEM_MASTER["Item_Master.aspx<br/>Item Master"]:::aspnetPage
                    ITEM_CATEGORY["Item_Category.aspx<br/>Item Category"]:::aspnetPage
                    ITEM_GROUP["Item_Group.aspx<br/>Item Group"]:::aspnetPage
                    ITEM_SPECIFICATION["Item_Specification.aspx<br/>Item Specification"]:::aspnetPage
                    ITEM_ALTERNATIVE["Item_Alternative.aspx<br/>Item Alternative"]:::aspnetPage
                    ITEM_SUPPLIER_MAPPING["Item_Supplier_Mapping.aspx<br/>Item Supplier Mapping"]:::aspnetPage
                    PRICE_MASTER["Price_Master.aspx<br/>Price Master"]:::aspnetPage
                    RATE_CONTRACT["Rate_Contract.aspx<br/>Rate Contract"]:::aspnetPage
                end
                
                %% Purchase Setup
                subgraph PURCHASE_SETUP["⚙️ Purchase Setup"]
                    PURCHASE_TYPE["Purchase_Type.aspx<br/>Purchase Type"]:::aspnetPage
                    PURCHASE_CATEGORY["Purchase_Category.aspx<br/>Purchase Category"]:::aspnetPage
                    DELIVERY_TERMS["Delivery_Terms.aspx<br/>Delivery Terms"]:::aspnetPage
                    PAYMENT_TERMS_PUR["Payment_Terms.aspx<br/>Payment Terms"]:::aspnetPage
                    INCOTERMS["Incoterms.aspx<br/>Incoterms"]:::aspnetPage
                    FREIGHT_TERMS["Freight_Terms.aspx<br/>Freight Terms"]:::aspnetPage
                    QUALITY_PARAMETERS["Quality_Parameters.aspx<br/>Quality Parameters"]:::aspnetPage
                    INSPECTION_CRITERIA["Inspection_Criteria.aspx<br/>Inspection Criteria"]:::aspnetPage
                end
                
                %% Approval Setup
                subgraph APPROVAL_SETUP["✅ Approval Setup"]
                    APPROVAL_MATRIX["Approval_Matrix.aspx<br/>Approval Matrix"]:::aspnetPage
                    AUTHORIZATION_LIMIT["Authorization_Limit.aspx<br/>Authorization Limit"]:::aspnetPage
                    PURCHASE_COMMITTEE["Purchase_Committee.aspx<br/>Purchase Committee"]:::aspnetPage
                    DELEGATION_MATRIX["Delegation_Matrix.aspx<br/>Delegation Matrix"]:::aspnetPage
                end
                
                MASTERS_CONFIG["Masters/Web.config<br/>Role: Purchase_Master"]:::aspnetPage
            end
            
            %% Transactions Section
            subgraph TRANSACTIONS["💼 Transactions Section"]
                direction TB
                TRANS_DASH["Transactions/Dashboard.aspx<br/>Transaction Menu"]:::aspnetPage
                
                %% Purchase Requisition
                subgraph PURCHASE_REQ["📝 Purchase Requisition"]
                    PR_DASH["PurchaseRequisition_Dashboard.aspx<br/>PR Dashboard"]:::aspnetPage
                    PR_NEW["PurchaseRequisition_New.aspx<br/>Create Purchase Requisition"]:::aspnetPage
                    PR_EDIT["PurchaseRequisition_Edit.aspx<br/>Edit Purchase Requisition"]:::aspnetPage
                    PR_DELETE["PurchaseRequisition_Delete.aspx<br/>Delete Purchase Requisition"]:::aspnetPage
                    PR_APPROVAL["PurchaseRequisition_Approval.aspx<br/>PR Approval"]:::aspnetPage
                    PR_CONSOLIDATION["PurchaseRequisition_Consolidation.aspx<br/>PR Consolidation"]:::aspnetPage
                    PR_PRINT["PurchaseRequisition_Print.aspx<br/>Print Purchase Requisition"]:::aspnetPage
                end
                
                %% Request for Quotation
                subgraph RFQ_MGMT["📋 Request for Quotation"]
                    RFQ_DASH["RFQ_Dashboard.aspx<br/>RFQ Dashboard"]:::aspnetPage
                    RFQ_NEW["RFQ_New.aspx<br/>Create RFQ"]:::aspnetPage
                    RFQ_EDIT["RFQ_Edit.aspx<br/>Edit RFQ"]:::aspnetPage
                    RFQ_DELETE["RFQ_Delete.aspx<br/>Delete RFQ"]:::aspnetPage
                    RFQ_SEND["RFQ_Send.aspx<br/>Send RFQ"]:::aspnetPage
                    RFQ_FOLLOWUP["RFQ_Followup.aspx<br/>RFQ Follow-up"]:::aspnetPage
                    RFQ_PRINT["RFQ_Print.aspx<br/>Print RFQ"]:::aspnetPage
                end
                
                %% Quotation Management
                subgraph QUOTATION_MGMT["💰 Quotation Management"]
                    QUOTATION_DASH["Quotation_Dashboard.aspx<br/>Quotation Dashboard"]:::aspnetPage
                    QUOTATION_NEW["Quotation_New.aspx<br/>Create Quotation"]:::aspnetPage
                    QUOTATION_EDIT["Quotation_Edit.aspx<br/>Edit Quotation"]:::aspnetPage
                    QUOTATION_DELETE["Quotation_Delete.aspx<br/>Delete Quotation"]:::aspnetPage
                    QUOTATION_COMPARISON["Quotation_Comparison.aspx<br/>Quotation Comparison"]:::aspnetPage
                    QUOTATION_ANALYSIS["Quotation_Analysis.aspx<br/>Quotation Analysis"]:::aspnetPage
                    QUOTATION_APPROVAL["Quotation_Approval.aspx<br/>Quotation Approval"]:::aspnetPage
                    QUOTATION_PRINT["Quotation_Print.aspx<br/>Print Quotation"]:::aspnetPage
                end
                
                %% Purchase Order Management
                subgraph PO_MGMT["📄 Purchase Order Management"]
                    PO_DASH["PurchaseOrder_Dashboard.aspx<br/>PO Dashboard"]:::aspnetPage
                    PO_NEW["PurchaseOrder_New.aspx<br/>Create Purchase Order"]:::aspnetPage
                    PO_EDIT["PurchaseOrder_Edit.aspx<br/>Edit Purchase Order"]:::aspnetPage
                    PO_DELETE["PurchaseOrder_Delete.aspx<br/>Delete Purchase Order"]:::aspnetPage
                    PO_APPROVAL["PurchaseOrder_Approval.aspx<br/>PO Approval"]:::aspnetPage
                    PO_AMENDMENT["PurchaseOrder_Amendment.aspx<br/>PO Amendment"]:::aspnetPage
                    PO_CANCELLATION["PurchaseOrder_Cancellation.aspx<br/>PO Cancellation"]:::aspnetPage
                    PO_CLOSURE["PurchaseOrder_Closure.aspx<br/>PO Closure"]:::aspnetPage
                    PO_PRINT["PurchaseOrder_Print.aspx<br/>Print Purchase Order"]:::aspnetPage
                end
                
                %% Goods Receipt Management
                subgraph GR_MGMT["📦 Goods Receipt Management"]
                    GR_DASH["GoodsReceipt_Dashboard.aspx<br/>GR Dashboard"]:::aspnetPage
                    GR_NEW["GoodsReceipt_New.aspx<br/>Create Goods Receipt"]:::aspnetPage
                    GR_EDIT["GoodsReceipt_Edit.aspx<br/>Edit Goods Receipt"]:::aspnetPage
                    GR_DELETE["GoodsReceipt_Delete.aspx<br/>Delete Goods Receipt"]:::aspnetPage
                    GR_INSPECTION["GoodsReceipt_Inspection.aspx<br/>GR Inspection"]:::aspnetPage
                    GR_ACCEPTANCE["GoodsReceipt_Acceptance.aspx<br/>GR Acceptance"]:::aspnetPage
                    GR_REJECTION["GoodsReceipt_Rejection.aspx<br/>GR Rejection"]:::aspnetPage
                    GR_PRINT["GoodsReceipt_Print.aspx<br/>Print Goods Receipt"]:::aspnetPage
                end
                
                %% Invoice Management
                subgraph INVOICE_MGMT["💰 Invoice Management"]
                    PURCHASE_INVOICE_DASH["PurchaseInvoice_Dashboard.aspx<br/>Purchase Invoice Dashboard"]:::aspnetPage
                    PURCHASE_INVOICE_NEW["PurchaseInvoice_New.aspx<br/>Create Purchase Invoice"]:::aspnetPage
                    PURCHASE_INVOICE_EDIT["PurchaseInvoice_Edit.aspx<br/>Edit Purchase Invoice"]:::aspnetPage
                    PURCHASE_INVOICE_DELETE["PurchaseInvoice_Delete.aspx<br/>Delete Purchase Invoice"]:::aspnetPage
                    PURCHASE_INVOICE_VERIFICATION["PurchaseInvoice_Verification.aspx<br/>Invoice Verification"]:::aspnetPage
                    PURCHASE_INVOICE_APPROVAL["PurchaseInvoice_Approval.aspx<br/>Invoice Approval"]:::aspnetPage
                    PURCHASE_INVOICE_PRINT["PurchaseInvoice_Print.aspx<br/>Print Purchase Invoice"]:::aspnetPage
                end
                
                %% Payment Management
                subgraph PAYMENT_MGMT["💳 Payment Management"]
                    PAYMENT_SCHEDULE["Payment_Schedule.aspx<br/>Payment Schedule"]:::aspnetPage
                    PAYMENT_PROCESSING["Payment_Processing.aspx<br/>Payment Processing"]:::aspnetPage
                    PAYMENT_APPROVAL["Payment_Approval.aspx<br/>Payment Approval"]:::aspnetPage
                    PAYMENT_VOUCHER["Payment_Voucher.aspx<br/>Payment Voucher"]:::aspnetPage
                    ADVANCE_PAYMENT["Advance_Payment.aspx<br/>Advance Payment"]:::aspnetPage
                    TDS_CALCULATION["TDS_Calculation.aspx<br/>TDS Calculation"]:::aspnetPage
                    PAYMENT_PRINT["Payment_Print.aspx<br/>Print Payment"]:::aspnetPage
                end
                
                %% Return Management
                subgraph RETURN_MGMT["🔄 Return Management"]
                    PURCHASE_RETURN_DASH["PurchaseReturn_Dashboard.aspx<br/>Purchase Return Dashboard"]:::aspnetPage
                    PURCHASE_RETURN_NEW["PurchaseReturn_New.aspx<br/>Create Purchase Return"]:::aspnetPage
                    PURCHASE_RETURN_EDIT["PurchaseReturn_Edit.aspx<br/>Edit Purchase Return"]:::aspnetPage
                    PURCHASE_RETURN_DELETE["PurchaseReturn_Delete.aspx<br/>Delete Purchase Return"]:::aspnetPage
                    PURCHASE_RETURN_APPROVAL["PurchaseReturn_Approval.aspx<br/>Return Approval"]:::aspnetPage
                    DEBIT_NOTE_PURCHASE["DebitNote_Purchase.aspx<br/>Purchase Debit Note"]:::aspnetPage
                    PURCHASE_RETURN_PRINT["PurchaseReturn_Print.aspx<br/>Print Purchase Return"]:::aspnetPage
                end
                
                %% Contract Management
                subgraph CONTRACT_MGMT["📋 Contract Management"]
                    CONTRACT_NEW["Contract_New.aspx<br/>Create Contract"]:::aspnetPage
                    CONTRACT_EDIT["Contract_Edit.aspx<br/>Edit Contract"]:::aspnetPage
                    CONTRACT_DELETE["Contract_Delete.aspx<br/>Delete Contract"]:::aspnetPage
                    CONTRACT_RENEWAL["Contract_Renewal.aspx<br/>Contract Renewal"]:::aspnetPage
                    CONTRACT_AMENDMENT["Contract_Amendment.aspx<br/>Contract Amendment"]:::aspnetPage
                    CONTRACT_CLOSURE["Contract_Closure.aspx<br/>Contract Closure"]:::aspnetPage
                    CONTRACT_PRINT["Contract_Print.aspx<br/>Print Contract"]:::aspnetPage
                end
                
                %% Vendor Management
                subgraph VENDOR_MGMT["👥 Vendor Management"]
                    VENDOR_REGISTRATION["Vendor_Registration.aspx<br/>Vendor Registration"]:::aspnetPage
                    VENDOR_QUALIFICATION["Vendor_Qualification.aspx<br/>Vendor Qualification"]:::aspnetPage
                    VENDOR_AUDIT["Vendor_Audit.aspx<br/>Vendor Audit"]:::aspnetPage
                    VENDOR_PERFORMANCE["Vendor_Performance.aspx<br/>Vendor Performance"]:::aspnetPage
                    VENDOR_DEVELOPMENT["Vendor_Development.aspx<br/>Vendor Development"]:::aspnetPage
                    VENDOR_PAYMENT_TERMS["Vendor_Payment_Terms.aspx<br/>Vendor Payment Terms"]:::aspnetPage
                end
            end

            %% Reports Section
            subgraph REPORTS["📊 Reports Section"]
                direction TB
                REP_DASH["Reports/Dashboard.aspx<br/>Reports Menu"]:::aspnetPage

                %% Purchase Reports
                subgraph PURCHASE_REPORTS["📈 Purchase Reports"]
                    PURCHASE_REGISTER["Purchase_Register.aspx<br/>Purchase Register"]:::aspnetPage
                    PURCHASE_SUMMARY["Purchase_Summary.aspx<br/>Purchase Summary"]:::aspnetPage
                    PURCHASE_ANALYSIS["Purchase_Analysis.aspx<br/>Purchase Analysis"]:::aspnetPage
                    SUPPLIER_WISE_PURCHASE["Supplier_Wise_Purchase.aspx<br/>Supplier-wise Purchase"]:::aspnetPage
                    ITEM_WISE_PURCHASE["Item_Wise_Purchase.aspx<br/>Item-wise Purchase"]:::aspnetPage
                    CATEGORY_WISE_PURCHASE["Category_Wise_Purchase.aspx<br/>Category-wise Purchase"]:::aspnetPage
                end

                %% Order Reports
                subgraph ORDER_REPORTS["📝 Order Reports"]
                    PO_REGISTER["PO_Register.aspx<br/>PO Register"]:::aspnetPage
                    PENDING_PO["Pending_PO.aspx<br/>Pending Purchase Orders"]:::aspnetPage
                    PO_STATUS_REPORT["PO_Status_Report.aspx<br/>PO Status Report"]:::aspnetPage
                    PO_DELIVERY_SCHEDULE["PO_Delivery_Schedule.aspx<br/>PO Delivery Schedule"]:::aspnetPage
                    OVERDUE_PO["Overdue_PO.aspx<br/>Overdue Purchase Orders"]:::aspnetPage
                end

                %% Supplier Reports
                subgraph SUPPLIER_REPORTS["👥 Supplier Reports"]
                    SUPPLIER_LEDGER["Supplier_Ledger.aspx<br/>Supplier Ledger"]:::aspnetPage
                    SUPPLIER_AGING["Supplier_Aging.aspx<br/>Supplier Aging"]:::aspnetPage
                    SUPPLIER_PERFORMANCE_REPORT["Supplier_Performance_Report.aspx<br/>Supplier Performance"]:::aspnetPage
                    SUPPLIER_EVALUATION_REPORT["Supplier_Evaluation_Report.aspx<br/>Supplier Evaluation"]:::aspnetPage
                    VENDOR_RATING_REPORT["Vendor_Rating_Report.aspx<br/>Vendor Rating"]:::aspnetPage
                end

                %% Financial Reports
                subgraph FINANCIAL_REPORTS["💰 Financial Reports"]
                    PURCHASE_COST_ANALYSIS["Purchase_Cost_Analysis.aspx<br/>Purchase Cost Analysis"]:::aspnetPage
                    PAYMENT_SCHEDULE_REPORT["Payment_Schedule_Report.aspx<br/>Payment Schedule"]:::aspnetPage
                    OUTSTANDING_PAYMENT["Outstanding_Payment.aspx<br/>Outstanding Payment"]:::aspnetPage
                    TDS_REPORT["TDS_Report.aspx<br/>TDS Report"]:::aspnetPage
                    ADVANCE_PAYMENT_REPORT["Advance_Payment_Report.aspx<br/>Advance Payment Report"]:::aspnetPage
                end

                %% Quality Reports
                subgraph QUALITY_REPORTS["✅ Quality Reports"]
                    INSPECTION_REPORT["Inspection_Report.aspx<br/>Inspection Report"]:::aspnetPage
                    REJECTION_REPORT["Rejection_Report.aspx<br/>Rejection Report"]:::aspnetPage
                    QUALITY_ANALYSIS["Quality_Analysis.aspx<br/>Quality Analysis"]:::aspnetPage
                    SUPPLIER_QUALITY_REPORT["Supplier_Quality_Report.aspx<br/>Supplier Quality"]:::aspnetPage
                end

                %% Crystal Reports
                subgraph CRYSTAL_REPORTS["💎 Crystal Reports"]
                    PR_RPT["PurchaseRequisition.rpt<br/>Purchase Requisition Report"]:::report
                    RFQ_RPT["RFQ.rpt<br/>RFQ Report"]:::report
                    QUOTATION_RPT["Quotation.rpt<br/>Quotation Report"]:::report
                    PO_RPT["PurchaseOrder.rpt<br/>Purchase Order Report"]:::report
                    GR_RPT["GoodsReceipt.rpt<br/>Goods Receipt Report"]:::report
                    PURCHASE_INVOICE_RPT["PurchaseInvoice.rpt<br/>Purchase Invoice Report"]:::report
                    PURCHASE_REGISTER_RPT["Purchase_Register.rpt<br/>Purchase Register Report"]:::report
                    SUPPLIER_LEDGER_RPT["Supplier_Ledger.rpt<br/>Supplier Ledger Report"]:::report
                    PAYMENT_VOUCHER_RPT["Payment_Voucher.rpt<br/>Payment Voucher Report"]:::report
                    CONTRACT_RPT["Contract.rpt<br/>Contract Report"]:::report
                end
            end
        end
