graph TB
    %% Color definitions
    classDef aspnetPage fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef djangoView fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef database fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    classDef businessLogic fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef report fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    classDef workflow fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    classDef integration fill:#fce4ec,stroke:#ad1457,stroke-width:2px,color:#000

    %% Main Accounts Module Structure
    subgraph ACCOUNTS_MODULE["💰 Accounts Module - Financial Management System"]
        direction TB
        
        %% Entry Points
        ACC_DASHBOARD[("📊 Accounts Dashboard<br/>Entry Point")]
        
        %% ASP.NET Structure
        subgraph ASP_NET["🌐 ASP.NET NewERP Structure"]
            direction TB
            
            %% Main Dashboard
            ASP_MAIN_DASH["Dashboard.aspx<br/>Main Accounts Dashboard"]:::aspnetPage
            
            %% Masters Section
            subgraph MASTERS["📋 Masters Section"]
                direction TB
                MASTERS_DASH["Masters/Dashboard.aspx<br/>Masters Menu"]:::aspnetPage
                
                %% Account Setup
                subgraph ACCOUNT_SETUP["🏦 Account Setup"]
                    ACC_HEAD["AccHead.aspx<br/>Account Head Master"]:::aspnetPage
                    BANK_MASTER["Bank.aspx<br/>Bank Master"]:::aspnetPage
                    CURRENCY["Currency.aspx<br/>Currency Master"]:::aspnetPage
                    PAYMENT_MODE["PaymentMode.aspx<br/>Payment Mode"]:::aspnetPage
                    PAYMENT_TERMS["PaymentTerms.aspx<br/>Payment Terms"]:::aspnetPage
                    PAID_TYPE["PaidType.aspx<br/>Paid Type"]:::aspnetPage
                    CHEQUE_SERIES["Cheque_series.aspx<br/>Cheque Series"]:::aspnetPage
                    CASH_BANK_ENTRY["Cash_Bank_Entry.aspx<br/>Cash Bank Entry"]:::aspnetPage
                end
                
                %% Tax Setup
                subgraph TAX_SETUP["💸 Tax Setup"]
                    VAT_MASTER["VAT.aspx<br/>VAT Master"]:::aspnetPage
                    EXCISE_MASTER["Excise.aspx<br/>Excise Master"]:::aspnetPage
                    EXCISABLE_COMMODITY["ExcisableCommodity.aspx<br/>Excisable Commodity"]:::aspnetPage
                    TDS_CODE["TDS_Code.aspx<br/>TDS Code"]:::aspnetPage
                    OCTORI["Octori.aspx<br/>Octori Master"]:::aspnetPage
                end
                
                %% Financial Setup
                subgraph FINANCIAL_SETUP["💼 Financial Setup"]
                    ASSET_MASTER["Asset.aspx<br/>Asset Master"]:::aspnetPage
                    LOAN_TYPE["LoanType.aspx<br/>Loan Type"]:::aspnetPage
                    INTEREST_TYPE["IntrestType.aspx<br/>Interest Type"]:::aspnetPage
                    INVOICE_AGAINST["InvoiceAgainst.aspx<br/>Invoice Against"]:::aspnetPage
                    PAYMENT_RECEIPT_AGAINST["Payement_Receipt_Against.aspx<br/>Payment Receipt Against"]:::aspnetPage
                end
                
                %% Other Masters
                subgraph OTHER_MASTERS["🔧 Other Masters"]
                    FREIGHT["Freight.aspx<br/>Freight Master"]:::aspnetPage
                    PACKING_FORWARDING["Packin_Forwarding.aspx<br/>Packing & Forwarding"]:::aspnetPage
                    WARRANTY_TERMS["WarrentyTerms.aspx<br/>Warranty Terms"]:::aspnetPage
                    IOU_REASONS["IOU_Reasons.aspx<br/>IOU Reasons"]:::aspnetPage
                    TOUR_EXPENSES["TourExpencess.aspx<br/>Tour Expenses"]:::aspnetPage
                end
                
                MASTERS_CONFIG["Masters/web.config<br/>Role: Accounts_Master"]:::aspnetPage
            end
            
            %% Transactions Section
            subgraph TRANSACTIONS["💼 Transactions Section"]
                direction TB
                TRANS_DASH["Transactions/Dashboard.aspx<br/>Transaction Menu"]:::aspnetPage
                
                %% Voucher Management
                subgraph VOUCHER_MGMT["📝 Voucher Management"]
                    %% Cash Vouchers
                    CASH_VOUCHER_NEW["CashVoucher_New.aspx<br/>Create Cash Voucher"]:::aspnetPage
                    CASH_VOUCHER_DELETE["CashVoucher_Delete.aspx<br/>Delete Cash Voucher"]:::aspnetPage
                    CASH_VOUCHER_PRINT["CashVoucher_Print.aspx<br/>Print Cash Voucher"]:::aspnetPage
                    CASH_VOUCHER_PAY_PRINT["CashVoucher_Payment_Print_Details.aspx<br/>Cash Payment Print"]:::aspnetPage
                    CASH_VOUCHER_REC_PRINT["CashVoucher_Receipt_Print_Details.aspx<br/>Cash Receipt Print"]:::aspnetPage
                    
                    %% Bank Vouchers
                    BANK_VOUCHER["BankVoucher.aspx<br/>Bank Voucher"]:::aspnetPage
                    BANK_VOUCHER_DELETE["BankVoucher_Delete.aspx<br/>Delete Bank Voucher"]:::aspnetPage
                    BANK_VOUCHER_PRINT["BankVoucher_Print.aspx<br/>Print Bank Voucher"]:::aspnetPage
                    BANK_VOUCHER_ADVICE["BankVoucher_Advice_print.aspx<br/>Bank Advice Print"]:::aspnetPage
                    
                    %% Contra Entry
                    CONTRA_ENTRY["ContraEntry.aspx<br/>Contra Entry"]:::aspnetPage
                end
                
                %% Invoice Management
                subgraph INVOICE_MGMT["📄 Invoice Management"]
                    %% Sales Invoice
                    subgraph SALES_INVOICE["💰 Sales Invoice"]
                        SALES_INV_DASH["SalesInvoice_Dashboard.aspx<br/>Sales Invoice Dashboard"]:::aspnetPage
                        SALES_INV_NEW["SalesInvoice_New.aspx<br/>Create Sales Invoice"]:::aspnetPage
                        SALES_INV_EDIT["SalesInvoice_Edit.aspx<br/>Edit Sales Invoice"]:::aspnetPage
                        SALES_INV_DELETE["SalesInvoice_Delete.aspx<br/>Delete Sales Invoice"]:::aspnetPage
                        SALES_INV_PRINT["SalesInvoice_Print.aspx<br/>Print Sales Invoice"]:::aspnetPage
                    end
                    
                    %% Proforma Invoice
                    subgraph PROFORMA_INVOICE["📋 Proforma Invoice"]
                        PROFORMA_NEW["ProformaInvoice_New.aspx<br/>Create Proforma Invoice"]:::aspnetPage
                        PROFORMA_EDIT["ProformaInvoice_Edit.aspx<br/>Edit Proforma Invoice"]:::aspnetPage
                        PROFORMA_DELETE["ProformaInvoice_Delete.aspx<br/>Delete Proforma Invoice"]:::aspnetPage
                        PROFORMA_PRINT["ProformaInvoice_Print.aspx<br/>Print Proforma Invoice"]:::aspnetPage
                    end
                    
                    %% Service Tax Invoice
                    subgraph SERVICE_TAX_INVOICE["🏷️ Service Tax Invoice"]
                        SERVICE_TAX_DASH["ServiceTaxInvoice_Dashboard.aspx<br/>Service Tax Dashboard"]:::aspnetPage
                        SERVICE_TAX_NEW["ServiceTaxInvoice_New.aspx<br/>Create Service Tax Invoice"]:::aspnetPage
                        SERVICE_TAX_EDIT["ServiceTaxInvoice_Edit.aspx<br/>Edit Service Tax Invoice"]:::aspnetPage
                        SERVICE_TAX_DELETE["ServiceTaxInvoice_Delete.aspx<br/>Delete Service Tax Invoice"]:::aspnetPage
                        SERVICE_TAX_PRINT["ServiceTaxInvoice_Print.aspx<br/>Print Service Tax Invoice"]:::aspnetPage
                    end
                end
                
                %% Bill Management
                subgraph BILL_MGMT["📑 Bill Management"]
                    BILL_BOOKING_NEW["BillBooking_New.aspx<br/>Create Bill Booking"]:::aspnetPage
                    BILL_BOOKING_EDIT["BillBooking_Edit.aspx<br/>Edit Bill Booking"]:::aspnetPage
                    BILL_BOOKING_DELETE["BillBooking_Delete.aspx<br/>Delete Bill Booking"]:::aspnetPage
                    BILL_BOOKING_AUTHORIZE["BillBooking_Authorize.aspx<br/>Authorize Bill Booking"]:::aspnetPage
                    BILL_BOOKING_PRINT["BillBooking_Print.aspx<br/>Print Bill Booking"]:::aspnetPage
                    BILL_BOOKING_ITEM_GRID["BillBooking_ItemGrid.aspx<br/>Bill Item Grid"]:::aspnetPage
                end
                
                %% Credit/Debit Notes
                subgraph CREDIT_DEBIT_NOTES["📝 Credit/Debit Notes"]
                    CREDIT_NOTE["Credit_Note.aspx<br/>Credit Note"]:::aspnetPage
                    DEBIT_NOTE["Debit_Note.aspx<br/>Debit Note"]:::aspnetPage
                end
                
                %% Financial Transactions
                subgraph FINANCIAL_TRANS["💼 Financial Transactions"]
                    %% Asset Management
                    ASSET_REGISTER["Asset_Register.aspx<br/>Asset Register"]:::aspnetPage
                    ASSET_REGISTER1["Asset_Register1.aspx<br/>Asset Register 1"]:::aspnetPage
                    ASSET_REGISTER_REPORT["AssetRegister_Report.aspx<br/>Asset Register Report"]:::aspnetPage
                    
                    %% Capital & Loans
                    CAPITAL["Capital.aspx<br/>Capital Management"]:::aspnetPage
                    ACC_LOAN_MASTER["ACC_LoanMaster.aspx<br/>Loan Master"]:::aspnetPage
                    CURRENT_LIABILITIES["CurrentLiabilities.aspx<br/>Current Liabilities"]:::aspnetPage
                    ACC_BAL_CURR_ASSETS["Acc_Bal_CurrAssets.aspx<br/>Balance Current Assets"]:::aspnetPage
                    
                    %% Capital Particulars
                    ACC_CAPITAL_PART["Acc_Capital_Particulars.aspx<br/>Capital Particulars"]:::aspnetPage
                    ACC_CAPITAL_PART_DET["Acc_Capital_Part_Details.aspx<br/>Capital Part Details"]:::aspnetPage
                    ACC_LOAN_PART["Acc_Loan_Particulars.aspx<br/>Loan Particulars"]:::aspnetPage
                    ACC_LOAN_PART_DET["Acc_Loan_Part_Details.aspx<br/>Loan Part Details"]:::aspnetPage
                end
                
                %% Sundry Management
                subgraph SUNDRY_MGMT["👥 Sundry Management"]
                    SUNDRY_CREDITORS["SundryCreditors.aspx<br/>Sundry Creditors"]:::aspnetPage
                    SUNDRY_CREDITORS_DETAILS["SundryCreditors_Details.aspx<br/>Sundry Creditors Details"]:::aspnetPage
                    SUNDRY_CREDITORS_LIST["SundryCreditors_InDetailList.aspx<br/>Sundry Creditors List"]:::aspnetPage
                    SUNDRY_CREDITORS_VIEW["SundryCreditors_InDetailView.aspx<br/>Sundry Creditors View"]:::aspnetPage
                    
                    CREDITORS_DEBITORS["CreditorsDebitors.aspx<br/>Creditors Debitors"]:::aspnetPage
                    CREDITORS_DEBITORS_LIST["CreditorsDebitors_InDetailList.aspx<br/>Creditors Debitors List"]:::aspnetPage
                    CREDITORS_DEBITORS_VIEW["CreditorsDebitors_InDetailView.aspx<br/>Creditors Debitors View"]:::aspnetPage
                    
                    ACC_SUNDRY_CUST_LIST["Acc_Sundry_CustList.aspx<br/>Sundry Customer List"]:::aspnetPage
                    ACC_SUNDRY_DETAILS["Acc_Sundry_Details.aspx<br/>Sundry Details"]:::aspnetPage
                end
                
                %% Other Transactions
                subgraph OTHER_TRANS["🔧 Other Transactions"]
                    %% Advice & Payment
                    ADVICE["Advice.aspx<br/>Advice"]:::aspnetPage
                    ADVICE_DELETE["Advice_Delete.aspx<br/>Delete Advice"]:::aspnetPage
                    ADVICE_PRINT["Advice_Print.aspx<br/>Print Advice"]:::aspnetPage
                    ADVICE_PRINT_ADVICE["Advice_Print_Advice.aspx<br/>Print Advice Details"]:::aspnetPage
                    
                    %% Bank Reconciliation
                    BANK_RECONCILIATION["BankReconciliation_New.aspx<br/>Bank Reconciliation"]:::aspnetPage
                    
                    %% Tour & IOU
                    TOUR_VOUCHER["TourVoucher.aspx<br/>Tour Voucher"]:::aspnetPage
                    TOUR_VOUCHER_EDIT["TourVoucher_Edit.aspx<br/>Edit Tour Voucher"]:::aspnetPage
                    TOUR_VOUCHER_DELETE["TourVoucher_Delete.aspx<br/>Delete Tour Voucher"]:::aspnetPage
                    TOUR_VOUCHER_PRINT["TourVoucher_Print.aspx<br/>Print Tour Voucher"]:::aspnetPage
                    IOU_PAYMENT_RECEIPT["IOU_PaymentReceipt.aspx<br/>IOU Payment Receipt"]:::aspnetPage
                    
                    %% Balance Sheet
                    BALANCE_SHEET["BalanceSheet.aspx<br/>Balance Sheet"]:::aspnetPage
                    
                    %% Pending Invoice
                    PENDING_INVOICE_PRINT["PendingForInvoice_Print.aspx<br/>Pending Invoice Print"]:::aspnetPage
                    
                    %% Mail Merge
                    MAIL_MERGE["MailMerge.aspx<br/>Mail Merge"]:::aspnetPage
                end
            end
