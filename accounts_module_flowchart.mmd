graph TB
    %% Color definitions
    classDef aspnetPage fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef djangoView fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef database fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    classDef businessLogic fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef report fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    classDef workflow fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    classDef integration fill:#fce4ec,stroke:#ad1457,stroke-width:2px,color:#000

    %% Main Accounts Module Structure
    subgraph ACCOUNTS_MODULE["💰 Accounts Module - Financial Management System"]
        direction TB
        
        %% Entry Points
        ACC_DASHBOARD[("📊 Accounts Dashboard<br/>Entry Point")]
        
        %% ASP.NET Structure
        subgraph ASP_NET["🌐 ASP.NET NewERP Structure"]
            direction TB
            
            %% Main Dashboard
            ASP_MAIN_DASH["Dashboard.aspx<br/>Main Accounts Dashboard"]:::aspnetPage
            
            %% Masters Section
            subgraph MASTERS["📋 Masters Section"]
                direction TB
                MASTERS_DASH["Masters/Dashboard.aspx<br/>Masters Menu"]:::aspnetPage
                
                %% Account Setup
                subgraph ACCOUNT_SETUP["🏦 Account Setup"]
                    ACC_HEAD["AccHead.aspx<br/>Account Head Master"]:::aspnetPage
                    BANK_MASTER["Bank.aspx<br/>Bank Master"]:::aspnetPage
                    CURRENCY["Currency.aspx<br/>Currency Master"]:::aspnetPage
                    PAYMENT_MODE["PaymentMode.aspx<br/>Payment Mode"]:::aspnetPage
                    PAYMENT_TERMS["PaymentTerms.aspx<br/>Payment Terms"]:::aspnetPage
                    PAID_TYPE["PaidType.aspx<br/>Paid Type"]:::aspnetPage
                    CHEQUE_SERIES["Cheque_series.aspx<br/>Cheque Series"]:::aspnetPage
                    CASH_BANK_ENTRY["Cash_Bank_Entry.aspx<br/>Cash Bank Entry"]:::aspnetPage
                end
                
                %% Tax Setup
                subgraph TAX_SETUP["💸 Tax Setup"]
                    VAT_MASTER["VAT.aspx<br/>VAT Master"]:::aspnetPage
                    EXCISE_MASTER["Excise.aspx<br/>Excise Master"]:::aspnetPage
                    EXCISABLE_COMMODITY["ExcisableCommodity.aspx<br/>Excisable Commodity"]:::aspnetPage
                    TDS_CODE["TDS_Code.aspx<br/>TDS Code"]:::aspnetPage
                    OCTORI["Octori.aspx<br/>Octori Master"]:::aspnetPage
                end
                
                %% Financial Setup
                subgraph FINANCIAL_SETUP["💼 Financial Setup"]
                    ASSET_MASTER["Asset.aspx<br/>Asset Master"]:::aspnetPage
                    LOAN_TYPE["LoanType.aspx<br/>Loan Type"]:::aspnetPage
                    INTEREST_TYPE["IntrestType.aspx<br/>Interest Type"]:::aspnetPage
                    INVOICE_AGAINST["InvoiceAgainst.aspx<br/>Invoice Against"]:::aspnetPage
                    PAYMENT_RECEIPT_AGAINST["Payement_Receipt_Against.aspx<br/>Payment Receipt Against"]:::aspnetPage
                end
                
                %% Other Masters
                subgraph OTHER_MASTERS["🔧 Other Masters"]
                    FREIGHT["Freight.aspx<br/>Freight Master"]:::aspnetPage
                    PACKING_FORWARDING["Packin_Forwarding.aspx<br/>Packing & Forwarding"]:::aspnetPage
                    WARRANTY_TERMS["WarrentyTerms.aspx<br/>Warranty Terms"]:::aspnetPage
                    IOU_REASONS["IOU_Reasons.aspx<br/>IOU Reasons"]:::aspnetPage
                    TOUR_EXPENSES["TourExpencess.aspx<br/>Tour Expenses"]:::aspnetPage
                end
                
                MASTERS_CONFIG["Masters/web.config<br/>Role: Accounts_Master"]:::aspnetPage
            end
            
            %% Transactions Section
            subgraph TRANSACTIONS["💼 Transactions Section"]
                direction TB
                TRANS_DASH["Transactions/Dashboard.aspx<br/>Transaction Menu"]:::aspnetPage
                
                %% Voucher Management
                subgraph VOUCHER_MGMT["📝 Voucher Management"]
                    %% Cash Vouchers
                    CASH_VOUCHER_NEW["CashVoucher_New.aspx<br/>Create Cash Voucher"]:::aspnetPage
                    CASH_VOUCHER_DELETE["CashVoucher_Delete.aspx<br/>Delete Cash Voucher"]:::aspnetPage
                    CASH_VOUCHER_PRINT["CashVoucher_Print.aspx<br/>Print Cash Voucher"]:::aspnetPage
                    CASH_VOUCHER_PAY_PRINT["CashVoucher_Payment_Print_Details.aspx<br/>Cash Payment Print"]:::aspnetPage
                    CASH_VOUCHER_REC_PRINT["CashVoucher_Receipt_Print_Details.aspx<br/>Cash Receipt Print"]:::aspnetPage
                    
                    %% Bank Vouchers
                    BANK_VOUCHER["BankVoucher.aspx<br/>Bank Voucher"]:::aspnetPage
                    BANK_VOUCHER_DELETE["BankVoucher_Delete.aspx<br/>Delete Bank Voucher"]:::aspnetPage
                    BANK_VOUCHER_PRINT["BankVoucher_Print.aspx<br/>Print Bank Voucher"]:::aspnetPage
                    BANK_VOUCHER_ADVICE["BankVoucher_Advice_print.aspx<br/>Bank Advice Print"]:::aspnetPage
                    
                    %% Contra Entry
                    CONTRA_ENTRY["ContraEntry.aspx<br/>Contra Entry"]:::aspnetPage
                end
                
                %% Invoice Management
                subgraph INVOICE_MGMT["📄 Invoice Management"]
                    %% Sales Invoice
                    subgraph SALES_INVOICE["💰 Sales Invoice"]
                        SALES_INV_DASH["SalesInvoice_Dashboard.aspx<br/>Sales Invoice Dashboard"]:::aspnetPage
                        SALES_INV_NEW["SalesInvoice_New.aspx<br/>Create Sales Invoice"]:::aspnetPage
                        SALES_INV_EDIT["SalesInvoice_Edit.aspx<br/>Edit Sales Invoice"]:::aspnetPage
                        SALES_INV_DELETE["SalesInvoice_Delete.aspx<br/>Delete Sales Invoice"]:::aspnetPage
                        SALES_INV_PRINT["SalesInvoice_Print.aspx<br/>Print Sales Invoice"]:::aspnetPage
                    end
                    
                    %% Proforma Invoice
                    subgraph PROFORMA_INVOICE["📋 Proforma Invoice"]
                        PROFORMA_NEW["ProformaInvoice_New.aspx<br/>Create Proforma Invoice"]:::aspnetPage
                        PROFORMA_EDIT["ProformaInvoice_Edit.aspx<br/>Edit Proforma Invoice"]:::aspnetPage
                        PROFORMA_DELETE["ProformaInvoice_Delete.aspx<br/>Delete Proforma Invoice"]:::aspnetPage
                        PROFORMA_PRINT["ProformaInvoice_Print.aspx<br/>Print Proforma Invoice"]:::aspnetPage
                    end
                    
                    %% Service Tax Invoice
                    subgraph SERVICE_TAX_INVOICE["🏷️ Service Tax Invoice"]
                        SERVICE_TAX_DASH["ServiceTaxInvoice_Dashboard.aspx<br/>Service Tax Dashboard"]:::aspnetPage
                        SERVICE_TAX_NEW["ServiceTaxInvoice_New.aspx<br/>Create Service Tax Invoice"]:::aspnetPage
                        SERVICE_TAX_EDIT["ServiceTaxInvoice_Edit.aspx<br/>Edit Service Tax Invoice"]:::aspnetPage
                        SERVICE_TAX_DELETE["ServiceTaxInvoice_Delete.aspx<br/>Delete Service Tax Invoice"]:::aspnetPage
                        SERVICE_TAX_PRINT["ServiceTaxInvoice_Print.aspx<br/>Print Service Tax Invoice"]:::aspnetPage
                    end
                end
                
                %% Bill Management
                subgraph BILL_MGMT["📑 Bill Management"]
                    BILL_BOOKING_NEW["BillBooking_New.aspx<br/>Create Bill Booking"]:::aspnetPage
                    BILL_BOOKING_EDIT["BillBooking_Edit.aspx<br/>Edit Bill Booking"]:::aspnetPage
                    BILL_BOOKING_DELETE["BillBooking_Delete.aspx<br/>Delete Bill Booking"]:::aspnetPage
                    BILL_BOOKING_AUTHORIZE["BillBooking_Authorize.aspx<br/>Authorize Bill Booking"]:::aspnetPage
                    BILL_BOOKING_PRINT["BillBooking_Print.aspx<br/>Print Bill Booking"]:::aspnetPage
                    BILL_BOOKING_ITEM_GRID["BillBooking_ItemGrid.aspx<br/>Bill Item Grid"]:::aspnetPage
                end
                
                %% Credit/Debit Notes
                subgraph CREDIT_DEBIT_NOTES["📝 Credit/Debit Notes"]
                    CREDIT_NOTE["Credit_Note.aspx<br/>Credit Note"]:::aspnetPage
                    DEBIT_NOTE["Debit_Note.aspx<br/>Debit Note"]:::aspnetPage
                end
                
                %% Financial Transactions
                subgraph FINANCIAL_TRANS["💼 Financial Transactions"]
                    %% Asset Management
                    ASSET_REGISTER["Asset_Register.aspx<br/>Asset Register"]:::aspnetPage
                    ASSET_REGISTER1["Asset_Register1.aspx<br/>Asset Register 1"]:::aspnetPage
                    ASSET_REGISTER_REPORT["AssetRegister_Report.aspx<br/>Asset Register Report"]:::aspnetPage
                    
                    %% Capital & Loans
                    CAPITAL["Capital.aspx<br/>Capital Management"]:::aspnetPage
                    ACC_LOAN_MASTER["ACC_LoanMaster.aspx<br/>Loan Master"]:::aspnetPage
                    CURRENT_LIABILITIES["CurrentLiabilities.aspx<br/>Current Liabilities"]:::aspnetPage
                    ACC_BAL_CURR_ASSETS["Acc_Bal_CurrAssets.aspx<br/>Balance Current Assets"]:::aspnetPage
                    
                    %% Capital Particulars
                    ACC_CAPITAL_PART["Acc_Capital_Particulars.aspx<br/>Capital Particulars"]:::aspnetPage
                    ACC_CAPITAL_PART_DET["Acc_Capital_Part_Details.aspx<br/>Capital Part Details"]:::aspnetPage
                    ACC_LOAN_PART["Acc_Loan_Particulars.aspx<br/>Loan Particulars"]:::aspnetPage
                    ACC_LOAN_PART_DET["Acc_Loan_Part_Details.aspx<br/>Loan Part Details"]:::aspnetPage
                end
                
                %% Sundry Management
                subgraph SUNDRY_MGMT["👥 Sundry Management"]
                    SUNDRY_CREDITORS["SundryCreditors.aspx<br/>Sundry Creditors"]:::aspnetPage
                    SUNDRY_CREDITORS_DETAILS["SundryCreditors_Details.aspx<br/>Sundry Creditors Details"]:::aspnetPage
                    SUNDRY_CREDITORS_LIST["SundryCreditors_InDetailList.aspx<br/>Sundry Creditors List"]:::aspnetPage
                    SUNDRY_CREDITORS_VIEW["SundryCreditors_InDetailView.aspx<br/>Sundry Creditors View"]:::aspnetPage
                    
                    CREDITORS_DEBITORS["CreditorsDebitors.aspx<br/>Creditors Debitors"]:::aspnetPage
                    CREDITORS_DEBITORS_LIST["CreditorsDebitors_InDetailList.aspx<br/>Creditors Debitors List"]:::aspnetPage
                    CREDITORS_DEBITORS_VIEW["CreditorsDebitors_InDetailView.aspx<br/>Creditors Debitors View"]:::aspnetPage
                    
                    ACC_SUNDRY_CUST_LIST["Acc_Sundry_CustList.aspx<br/>Sundry Customer List"]:::aspnetPage
                    ACC_SUNDRY_DETAILS["Acc_Sundry_Details.aspx<br/>Sundry Details"]:::aspnetPage
                end
                
                %% Other Transactions
                subgraph OTHER_TRANS["🔧 Other Transactions"]
                    %% Advice & Payment
                    ADVICE["Advice.aspx<br/>Advice"]:::aspnetPage
                    ADVICE_DELETE["Advice_Delete.aspx<br/>Delete Advice"]:::aspnetPage
                    ADVICE_PRINT["Advice_Print.aspx<br/>Print Advice"]:::aspnetPage
                    ADVICE_PRINT_ADVICE["Advice_Print_Advice.aspx<br/>Print Advice Details"]:::aspnetPage
                    
                    %% Bank Reconciliation
                    BANK_RECONCILIATION["BankReconciliation_New.aspx<br/>Bank Reconciliation"]:::aspnetPage
                    
                    %% Tour & IOU
                    TOUR_VOUCHER["TourVoucher.aspx<br/>Tour Voucher"]:::aspnetPage
                    TOUR_VOUCHER_EDIT["TourVoucher_Edit.aspx<br/>Edit Tour Voucher"]:::aspnetPage
                    TOUR_VOUCHER_DELETE["TourVoucher_Delete.aspx<br/>Delete Tour Voucher"]:::aspnetPage
                    TOUR_VOUCHER_PRINT["TourVoucher_Print.aspx<br/>Print Tour Voucher"]:::aspnetPage
                    IOU_PAYMENT_RECEIPT["IOU_PaymentReceipt.aspx<br/>IOU Payment Receipt"]:::aspnetPage
                    
                    %% Balance Sheet
                    BALANCE_SHEET["BalanceSheet.aspx<br/>Balance Sheet"]:::aspnetPage
                    
                    %% Pending Invoice
                    PENDING_INVOICE_PRINT["PendingForInvoice_Print.aspx<br/>Pending Invoice Print"]:::aspnetPage
                    
                    %% Mail Merge
                    MAIL_MERGE["MailMerge.aspx<br/>Mail Merge"]:::aspnetPage
                end
            end

            %% Reports Section
            subgraph REPORTS["📊 Reports Section"]
                direction TB
                REP_DASH["Reports/Dashboard.aspx<br/>Reports Menu"]:::aspnetPage

                %% Financial Reports
                subgraph FINANCIAL_REPORTS["💼 Financial Reports"]
                    CASH_BANK_REGISTER["Cash_Bank_Register.aspx<br/>Cash Bank Register"]:::aspnetPage
                    PURCHASE_REPORT["Purchase_Reprt.aspx<br/>Purchase Report"]:::aspnetPage
                    SALES_REGISTER["Sales_Register.aspx<br/>Sales Register"]:::aspnetPage
                    VAT_REGISTER["Vat_Register.aspx<br/>VAT Register"]:::aspnetPage
                    PURCHASE_VAT_REGISTER["PurchaseVAT_Register.aspx<br/>Purchase VAT Register"]:::aspnetPage
                end

                %% Search & Analysis
                subgraph SEARCH_ANALYSIS["🔍 Search & Analysis"]
                    SEARCH_REPORT["Search.aspx<br/>General Search"]:::aspnetPage
                    SEARCH_DETAILS["Search_Details.aspx<br/>Search Details"]:::aspnetPage
                end

                %% Crystal Reports
                subgraph CRYSTAL_REPORTS["💎 Crystal Reports"]
                    %% Voucher Reports
                    CASH_VOUCHER_PAYMENT_RPT["CashVoucher_Payment.rpt<br/>Cash Payment Voucher"]:::report
                    CASH_VOUCHER_RECEIPT_RPT["CashVoucher_Receipt.rpt<br/>Cash Receipt Voucher"]:::report
                    BANK_VOUCHER_PAYMENT_RPT["BankVoucher_Payment_Print.rpt<br/>Bank Payment Voucher"]:::report
                    BANK_VOUCHER_AXIS_RPT["BankVoucher_Payment_Axis_Print.rpt<br/>Axis Bank Voucher"]:::report
                    BANK_VOUCHER_DENA_RPT["BankVoucher_Payment_Dena_Print.rpt<br/>Dena Bank Voucher"]:::report
                    BANK_VOUCHER_IDBI_RPT["BankVoucher_Payment_IDBI_Print.rpt<br/>IDBI Bank Voucher"]:::report
                    BANK_VOUCHER_ADVICE_RPT["BankVoucher_Payment_Advice.rpt<br/>Bank Payment Advice"]:::report

                    %% Invoice Reports
                    SALES_INVOICE_RPT["SalesInvoice.rpt<br/>Sales Invoice Report"]:::report
                    PROFORMA_INVOICE_RPT["ProformaInvoice.rpt<br/>Proforma Invoice Report"]:::report
                    SERVICE_TAX_INVOICE_RPT["ServiceTaxInvoice.rpt<br/>Service Tax Invoice"]:::report

                    %% Register Reports
                    CASH_BANK_REGISTER_RPT["Cash_Bank_Register.rpt<br/>Cash Bank Register"]:::report
                    SALES_VAT_RPT["Sales_Vat.rpt<br/>Sales VAT Report"]:::report
                    PURCHASE_VAT_RPT["Purchase_Vat.rpt<br/>Purchase VAT Report"]:::report
                    PURCHASE_RPT["Purchase.rpt<br/>Purchase Report"]:::report
                    PURCHASE_EXCISE_RPT["Purchase_Excise.rpt<br/>Purchase Excise Report"]:::report

                    %% Tax Reports
                    SALES_EX_PRINT_RPT["SalesEx_Print.rpt<br/>Sales Excise Print"]:::report
                    SALES_EXCISE_PRINT_RPT["SalesExise_Print.rpt<br/>Sales Excise Report"]:::report

                    %% Other Reports
                    BILL_BOOKING_RPT["BillBooking.rpt<br/>Bill Booking Report"]:::report
                    ASSET_REGISTER_RPT["AssetRegister.rpt<br/>Asset Register Report"]:::report
                    ADVICE_PRINT_RPT["Advice_Print.rpt<br/>Advice Print Report"]:::report
                    ADVICE_PRINT_ADVICE_RPT["Advice_Print_Advice.rpt<br/>Advice Print Details"]:::report
                    PENDING_INVOICE_RPT["PendingForInvoice.rpt<br/>Pending Invoice Report"]:::report
                    SUNDRY_CREDITORS_RPT["SundryCreditors_InDetailList.rpt<br/>Sundry Creditors Detail"]:::report
                    CREDITORS_DEBITORS_RPT["CreditorsDebitors_InDetailList.rpt<br/>Creditors Debitors Detail"]:::report
                    ACC_SUNDRY_DR_RPT["Acc_Sundry_Dr_Details.rpt<br/>Sundry Debtors Details"]:::report
                end
            end
        end

        %% Django Cortex Structure
        subgraph DJANGO_CORTEX["🐍 Django Cortex Structure"]
            direction TB

            %% Django Dashboard
            DJANGO_DASH["AccountsDashboardView<br/>Main Dashboard"]:::djangoView

            %% Master Data Management
            subgraph DJANGO_MASTERS["📋 Master Data Management"]
                %% Account Setup
                ACC_HEAD_LIST["AccountHeadListView<br/>Account Head List"]:::djangoView
                ACC_HEAD_CREATE["AccountHeadCreateView<br/>Create Account Head"]:::djangoView
                ACC_HEAD_UPDATE["AccountHeadUpdateView<br/>Update Account Head"]:::djangoView
                ACC_HEAD_DELETE["AccountHeadDeleteView<br/>Delete Account Head"]:::djangoView

                BANK_LIST["BankListView<br/>Bank List"]:::djangoView
                BANK_CREATE["BankCreateView<br/>Create Bank"]:::djangoView
                BANK_UPDATE["BankUpdateView<br/>Update Bank"]:::djangoView
                BANK_DELETE["BankDeleteView<br/>Delete Bank"]:::djangoView

                CURRENCY_LIST["CurrencyListView<br/>Currency List"]:::djangoView
                CURRENCY_CREATE["CurrencyCreateView<br/>Create Currency"]:::djangoView

                PAYMENT_MODE_LIST["PaymentModeListView<br/>Payment Mode List"]:::djangoView
                PAYMENT_MODE_CREATE["PaymentModeCreateView<br/>Create Payment Mode"]:::djangoView

                %% Tax Setup
                VAT_LIST["VATListView<br/>VAT List"]:::djangoView
                VAT_CREATE["VATCreateView<br/>Create VAT"]:::djangoView
                VAT_UPDATE["VATUpdateView<br/>Update VAT"]:::djangoView

                EXCISE_LIST["ExciseListView<br/>Excise List"]:::djangoView
                EXCISE_CREATE["ExciseCreateView<br/>Create Excise"]:::djangoView
                EXCISE_UPDATE["ExciseUpdateView<br/>Update Excise"]:::djangoView

                TDS_LIST["TDSCodeListView<br/>TDS Code List"]:::djangoView
                TDS_CREATE["TDSCodeCreateView<br/>Create TDS Code"]:::djangoView
                TDS_UPDATE["TDSCodeUpdateView<br/>Update TDS Code"]:::djangoView
            end

            %% Voucher Management
            subgraph DJANGO_VOUCHERS["📝 Voucher Management"]
                %% Cash Vouchers
                CASH_VOUCHER_LIST["CashVoucherListView<br/>Cash Voucher List"]:::djangoView
                CASH_VOUCHER_CREATE["CashVoucherCreateView<br/>Create Cash Voucher"]:::djangoView
                CASH_VOUCHER_UPDATE["CashVoucherUpdateView<br/>Update Cash Voucher"]:::djangoView
                CASH_VOUCHER_DELETE["CashVoucherDeleteView<br/>Delete Cash Voucher"]:::djangoView
                CASH_VOUCHER_APPROVAL["CashVoucherApprovalView<br/>Cash Voucher Approval"]:::djangoView

                %% Bank Vouchers
                BANK_VOUCHER_LIST["BankVoucherListView<br/>Bank Voucher List"]:::djangoView
                BANK_VOUCHER_CREATE["BankVoucherCreateView<br/>Create Bank Voucher"]:::djangoView
                BANK_VOUCHER_UPDATE["BankVoucherUpdateView<br/>Update Bank Voucher"]:::djangoView
                BANK_VOUCHER_DELETE["BankVoucherDeleteView<br/>Delete Bank Voucher"]:::djangoView
                BANK_VOUCHER_APPROVAL["BankVoucherApprovalView<br/>Bank Voucher Approval"]:::djangoView

                %% Contra Entry
                CONTRA_ENTRY_LIST["ContraEntryListView<br/>Contra Entry List"]:::djangoView
                CONTRA_ENTRY_CREATE["ContraEntryCreateView<br/>Create Contra Entry"]:::djangoView
                CONTRA_ENTRY_UPDATE["ContraEntryUpdateView<br/>Update Contra Entry"]:::djangoView
            end

            %% Invoice Management
            subgraph DJANGO_INVOICES["📄 Invoice Management"]
                %% Sales Invoice
                SALES_INV_LIST["SalesInvoiceListView<br/>Sales Invoice List"]:::djangoView
                SALES_INV_CREATE["SalesInvoiceCreateView<br/>Create Sales Invoice"]:::djangoView
                SALES_INV_UPDATE["SalesInvoiceUpdateView<br/>Update Sales Invoice"]:::djangoView
                SALES_INV_DELETE["SalesInvoiceDeleteView<br/>Delete Sales Invoice"]:::djangoView
                SALES_INV_APPROVAL["SalesInvoiceApprovalView<br/>Sales Invoice Approval"]:::djangoView

                %% Proforma Invoice
                PROFORMA_LIST["ProformaInvoiceListView<br/>Proforma Invoice List"]:::djangoView
                PROFORMA_CREATE["ProformaInvoiceCreateView<br/>Create Proforma Invoice"]:::djangoView
                PROFORMA_UPDATE["ProformaInvoiceUpdateView<br/>Update Proforma Invoice"]:::djangoView
                PROFORMA_DELETE["ProformaInvoiceDeleteView<br/>Delete Proforma Invoice"]:::djangoView

                %% Service Tax Invoice
                SERVICE_TAX_LIST["ServiceTaxInvoiceListView<br/>Service Tax Invoice List"]:::djangoView
                SERVICE_TAX_CREATE["ServiceTaxInvoiceCreateView<br/>Create Service Tax Invoice"]:::djangoView
                SERVICE_TAX_UPDATE["ServiceTaxInvoiceUpdateView<br/>Update Service Tax Invoice"]:::djangoView
                SERVICE_TAX_DELETE["ServiceTaxInvoiceDeleteView<br/>Delete Service Tax Invoice"]:::djangoView
            end

            %% Bill Management
            subgraph DJANGO_BILLS["📑 Bill Management"]
                BILL_BOOKING_LIST["BillBookingListView<br/>Bill Booking List"]:::djangoView
                BILL_BOOKING_CREATE["BillBookingCreateView<br/>Create Bill Booking"]:::djangoView
                BILL_BOOKING_UPDATE["BillBookingUpdateView<br/>Update Bill Booking"]:::djangoView
                BILL_BOOKING_DELETE["BillBookingDeleteView<br/>Delete Bill Booking"]:::djangoView
                BILL_BOOKING_AUTH["BillBookingAuthorizationView<br/>Bill Authorization"]:::djangoView
                BILL_BOOKING_APPROVAL["BillBookingApprovalView<br/>Bill Approval"]:::djangoView
            end

            %% Credit/Debit Notes
            subgraph DJANGO_NOTES["📝 Credit/Debit Notes"]
                CREDIT_NOTE_LIST["CreditNoteListView<br/>Credit Note List"]:::djangoView
                CREDIT_NOTE_CREATE["CreditNoteCreateView<br/>Create Credit Note"]:::djangoView
                CREDIT_NOTE_UPDATE["CreditNoteUpdateView<br/>Update Credit Note"]:::djangoView
                CREDIT_NOTE_DELETE["CreditNoteDeleteView<br/>Delete Credit Note"]:::djangoView

                DEBIT_NOTE_LIST["DebitNoteListView<br/>Debit Note List"]:::djangoView
                DEBIT_NOTE_CREATE["DebitNoteCreateView<br/>Create Debit Note"]:::djangoView
                DEBIT_NOTE_UPDATE["DebitNoteUpdateView<br/>Update Debit Note"]:::djangoView
                DEBIT_NOTE_DELETE["DebitNoteDeleteView<br/>Delete Debit Note"]:::djangoView
            end

            %% Financial Management
            subgraph DJANGO_FINANCIAL["💼 Financial Management"]
                ASSET_LIST["AssetListView<br/>Asset List"]:::djangoView
                ASSET_CREATE["AssetCreateView<br/>Create Asset"]:::djangoView
                ASSET_UPDATE["AssetUpdateView<br/>Update Asset"]:::djangoView
                ASSET_DELETE["AssetDeleteView<br/>Delete Asset"]:::djangoView
                ASSET_REGISTER_VIEW["AssetRegisterView<br/>Asset Register"]:::djangoView

                CAPITAL_LIST["CapitalListView<br/>Capital List"]:::djangoView
                CAPITAL_CREATE["CapitalCreateView<br/>Create Capital"]:::djangoView
                CAPITAL_UPDATE["CapitalUpdateView<br/>Update Capital"]:::djangoView

                LOAN_LIST["LoanListView<br/>Loan List"]:::djangoView
                LOAN_CREATE["LoanCreateView<br/>Create Loan"]:::djangoView
                LOAN_UPDATE["LoanUpdateView<br/>Update Loan"]:::djangoView

                CURRENT_LIABILITIES_LIST["CurrentLiabilitiesListView<br/>Current Liabilities"]:::djangoView
                CURRENT_ASSETS_LIST["CurrentAssetsListView<br/>Current Assets"]:::djangoView
            end

            %% Sundry Management
            subgraph DJANGO_SUNDRY["👥 Sundry Management"]
                SUNDRY_CREDITORS_LIST["SundryCreditorListView<br/>Sundry Creditor List"]:::djangoView
                SUNDRY_CREDITORS_CREATE["SundryCreditorCreateView<br/>Create Sundry Creditor"]:::djangoView
                SUNDRY_CREDITORS_UPDATE["SundryCreditorUpdateView<br/>Update Sundry Creditor"]:::djangoView
                SUNDRY_CREDITORS_DELETE["SundryCreditorDeleteView<br/>Delete Sundry Creditor"]:::djangoView

                CREDITORS_DEBITORS_LIST["CreditorsDebitorsListView<br/>Creditors Debitors List"]:::djangoView
                CREDITORS_DEBITORS_CREATE["CreditorsDebitorsCreateView<br/>Create Creditors Debitors"]:::djangoView
                CREDITORS_DEBITORS_UPDATE["CreditorsDebitorsUpdateView<br/>Update Creditors Debitors"]:::djangoView

                SUNDRY_CUSTOMER_LIST["SundryCustomerListView<br/>Sundry Customer List"]:::djangoView
                SUNDRY_DETAILS_VIEW["SundryDetailsView<br/>Sundry Details"]:::djangoView
            end

            %% Other Transactions
            subgraph DJANGO_OTHER_TRANS["🔧 Other Transactions"]
                ADVICE_LIST["AdviceListView<br/>Advice List"]:::djangoView
                ADVICE_CREATE["AdviceCreateView<br/>Create Advice"]:::djangoView
                ADVICE_UPDATE["AdviceUpdateView<br/>Update Advice"]:::djangoView
                ADVICE_DELETE["AdviceDeleteView<br/>Delete Advice"]:::djangoView

                BANK_RECONCILIATION_LIST["BankReconciliationListView<br/>Bank Reconciliation List"]:::djangoView
                BANK_RECONCILIATION_CREATE["BankReconciliationCreateView<br/>Create Bank Reconciliation"]:::djangoView

                TOUR_VOUCHER_LIST["TourVoucherListView<br/>Tour Voucher List"]:::djangoView
                TOUR_VOUCHER_CREATE["TourVoucherCreateView<br/>Create Tour Voucher"]:::djangoView
                TOUR_VOUCHER_UPDATE["TourVoucherUpdateView<br/>Update Tour Voucher"]:::djangoView
                TOUR_VOUCHER_DELETE["TourVoucherDeleteView<br/>Delete Tour Voucher"]:::djangoView

                IOU_PAYMENT_LIST["IOUPaymentReceiptListView<br/>IOU Payment Receipt List"]:::djangoView
                IOU_PAYMENT_CREATE["IOUPaymentReceiptCreateView<br/>Create IOU Payment Receipt"]:::djangoView

                BALANCE_SHEET_VIEW["BalanceSheetView<br/>Balance Sheet"]:::djangoView
                PENDING_INVOICE_VIEW["PendingInvoiceView<br/>Pending Invoice"]:::djangoView
                MAIL_MERGE_VIEW["MailMergeView<br/>Mail Merge"]:::djangoView
            end

            %% Reporting
            subgraph DJANGO_REPORTING["📊 Accounts Reporting"]
                ACCOUNTS_REPORTS_DASH["AccountsReportsDashboardView<br/>Reports Dashboard"]:::djangoView
                FINANCIAL_REPORTS_VIEW["FinancialReportsView<br/>Financial Reports"]:::djangoView
                CASH_BANK_REGISTER_VIEW["CashBankRegisterView<br/>Cash Bank Register"]:::djangoView
                PURCHASE_REPORT_VIEW["PurchaseReportView<br/>Purchase Report"]:::djangoView
                SALES_REGISTER_VIEW["SalesRegisterView<br/>Sales Register"]:::djangoView
                VAT_REGISTER_VIEW["VATRegisterView<br/>VAT Register"]:::djangoView
                SEARCH_VIEW["AccountsSearchView<br/>Accounts Search"]:::djangoView
            end
        end

        %% Database Layer
        subgraph DATABASE["🗄️ Database Layer"]
            direction TB

            %% Core Accounts Tables
            subgraph CORE_ACC_TABLES["📊 Core Accounts Tables"]
                TBL_ACC_HEAD["tblACC_AccHead<br/>Account Head Master"]:::database
                TBL_BANK_MASTER["tblACC_Bank_Master<br/>Bank Master"]:::database
                TBL_CURRENCY["tblACC_Currency<br/>Currency Master"]:::database
                TBL_PAYMENT_MODE["tblACC_PaymentMode<br/>Payment Mode"]:::database
                TBL_PAYMENT_TERMS["tblACC_PaymentTerms<br/>Payment Terms"]:::database
                TBL_PAID_TYPE["tblACC_PaidType<br/>Paid Type"]:::database
                TBL_CHEQUE_SERIES["tblACC_Cheque_Series<br/>Cheque Series"]:::database
            end

            %% Tax Tables
            subgraph TAX_TABLES["💸 Tax Tables"]
                TBL_VAT["tblACC_VAT<br/>VAT Master"]:::database
                TBL_EXCISE["tblACC_Excise<br/>Excise Master"]:::database
                TBL_EXCISABLE_COMMODITY["tblACC_ExcisableCommodity<br/>Excisable Commodity"]:::database
                TBL_TDS_CODE["tblACC_TDS_Code<br/>TDS Code"]:::database
                TBL_OCTORI["tblACC_Octori<br/>Octori Master"]:::database
            end

            %% Financial Tables
            subgraph FINANCIAL_TABLES["💼 Financial Tables"]
                TBL_ASSET["tblACC_Asset<br/>Asset Master"]:::database
                TBL_LOAN_TYPE["tblACC_LoanType<br/>Loan Type"]:::database
                TBL_INTEREST_TYPE["tblACC_InterestType<br/>Interest Type"]:::database
                TBL_INVOICE_AGAINST["tblACC_InvoiceAgainst<br/>Invoice Against"]:::database
                TBL_PAYMENT_RECEIPT_AGAINST["tblACC_Payment_Receipt_Against<br/>Payment Receipt Against"]:::database
                TBL_FREIGHT["tblACC_Freight<br/>Freight Master"]:::database
                TBL_PACKING_FORWARDING["tblACC_Packing_Forwarding<br/>Packing & Forwarding"]:::database
                TBL_WARRANTY_TERMS["tblACC_WarrantyTerms<br/>Warranty Terms"]:::database
                TBL_IOU_REASONS["tblACC_IOU_Reasons<br/>IOU Reasons"]:::database
                TBL_TOUR_EXPENSES["tblACC_TourExpenses<br/>Tour Expenses"]:::database
            end

            %% Transaction Tables
            subgraph TRANSACTION_TABLES["💼 Transaction Tables"]
                %% Voucher Tables
                TBL_CASH_VOUCHER_MASTER["tblACC_CashVoucher_Master<br/>Cash Voucher Master"]:::database
                TBL_CASH_VOUCHER_DETAILS["tblACC_CashVoucher_Details<br/>Cash Voucher Details"]:::database
                TBL_BANK_VOUCHER_MASTER["tblACC_BankVoucher_Master<br/>Bank Voucher Master"]:::database
                TBL_BANK_VOUCHER_DETAILS["tblACC_BankVoucher_Details<br/>Bank Voucher Details"]:::database
                TBL_CONTRA_ENTRY_MASTER["tblACC_ContraEntry_Master<br/>Contra Entry Master"]:::database
                TBL_CONTRA_ENTRY_DETAILS["tblACC_ContraEntry_Details<br/>Contra Entry Details"]:::database

                %% Invoice Tables
                TBL_SALES_INVOICE_MASTER["tblACC_SalesInvoice_Master<br/>Sales Invoice Master"]:::database
                TBL_SALES_INVOICE_DETAILS["tblACC_SalesInvoice_Details<br/>Sales Invoice Details"]:::database
                TBL_PROFORMA_INVOICE_MASTER["tblACC_ProformaInvoice_Master<br/>Proforma Invoice Master"]:::database
                TBL_PROFORMA_INVOICE_DETAILS["tblACC_ProformaInvoice_Details<br/>Proforma Invoice Details"]:::database
                TBL_SERVICE_TAX_INVOICE_MASTER["tblACC_ServiceTaxInvoice_Master<br/>Service Tax Invoice Master"]:::database
                TBL_SERVICE_TAX_INVOICE_DETAILS["tblACC_ServiceTaxInvoice_Details<br/>Service Tax Invoice Details"]:::database

                %% Bill Tables
                TBL_BILL_BOOKING_MASTER["tblACC_BillBooking_Master<br/>Bill Booking Master"]:::database
                TBL_BILL_BOOKING_DETAILS["tblACC_BillBooking_Details<br/>Bill Booking Details"]:::database

                %% Credit/Debit Note Tables
                TBL_CREDIT_NOTE_MASTER["tblACC_CreditNote_Master<br/>Credit Note Master"]:::database
                TBL_CREDIT_NOTE_DETAILS["tblACC_CreditNote_Details<br/>Credit Note Details"]:::database
                TBL_DEBIT_NOTE_MASTER["tblACC_DebitNote_Master<br/>Debit Note Master"]:::database
                TBL_DEBIT_NOTE_DETAILS["tblACC_DebitNote_Details<br/>Debit Note Details"]:::database
            end

            %% Integration Tables
            subgraph INTEGRATION_TABLES["🔗 Integration Tables"]
                TBL_COMPANY["tblCompany_master<br/>Company Information"]:::database
                TBL_FINANCIAL_YEAR["tblFinancial_master<br/>Financial Years"]:::database
                TBL_HR_STAFF["tblHR_OfficeStaff<br/>Employee Data"]:::database
                TBL_ITEM_MASTER["tblInv_Item_Master<br/>Item Master"]:::database
                TBL_SUPPLIER_MASTER["tblPur_Supplier_Master<br/>Supplier Master"]:::database
                TBL_CUSTOMER_MASTER["SD_Cust_Master<br/>Customer Master"]:::database
            end
        end

        %% Business Logic Layer
        subgraph BUSINESS_LOGIC["⚙️ Business Logic Layer"]
            direction TB

            %% ASP.NET Business Logic
            subgraph ASP_LOGIC["🌐 ASP.NET Logic"]
                CLS_FUNCTIONS["clsFunctions<br/>Common Functions"]:::businessLogic
                VOUCHER_PROCESSOR["Voucher Processor<br/>Voucher Processing Logic"]:::businessLogic
                INVOICE_GENERATOR["Invoice Generator<br/>Invoice Generation Engine"]:::businessLogic
                TAX_CALCULATOR["Tax Calculator<br/>Tax Calculation Engine"]:::businessLogic
                FINANCIAL_CALCULATOR["Financial Calculator<br/>Financial Calculations"]:::businessLogic
                REPORT_GENERATOR["Report Generator<br/>Crystal Reports Engine"]:::businessLogic
                EXPORT_EXCEL["ExportToExcel<br/>Excel Export"]:::businessLogic
                APPROVAL_ENGINE["Approval Engine<br/>Workflow Management"]:::businessLogic
                BANK_INTEGRATION["Bank Integration<br/>Bank API Integration"]:::businessLogic
                MAIL_MERGE_ENGINE["Mail Merge Engine<br/>Document Generation"]:::businessLogic
            end

            %% Django Business Logic
            subgraph DJANGO_LOGIC["🐍 Django Logic"]
                ACCOUNTS_FORMS["Accounts Forms<br/>Validation & Processing"]:::businessLogic
                ACCOUNTS_MODELS["Accounts Models<br/>Data Management"]:::businessLogic
                VOUCHER_ENGINE["Voucher Engine<br/>Voucher Processing"]:::businessLogic
                INVOICE_ENGINE["Invoice Engine<br/>Invoice Management"]:::businessLogic
                TAX_ENGINE["Tax Engine<br/>Tax Calculations"]:::businessLogic
                FINANCIAL_ENGINE["Financial Engine<br/>Financial Processing"]:::businessLogic
                APPROVAL_WORKFLOW["Approval Workflow<br/>Django Workflow"]:::businessLogic
                REPORTING_ENGINE["Reporting Engine<br/>Report Generation"]:::businessLogic
                INTEGRATION_ENGINE["Integration Engine<br/>Module Integration"]:::businessLogic
                AUDIT_ENGINE["Audit Engine<br/>Audit Trail Management"]:::businessLogic
            end
        end

        %% Workflow Processes
        subgraph WORKFLOWS["🔄 Workflow Processes"]
            direction TB

            %% Voucher Workflow
            subgraph VOUCHER_WORKFLOW["📝 Voucher Workflow"]
                WF_VOUCHER_CREATE["1. Create Voucher"]:::workflow
                WF_VOUCHER_VALIDATE["2. Validate Entries"]:::workflow
                WF_VOUCHER_APPROVE["3. Approve Voucher"]:::workflow
                WF_VOUCHER_POST["4. Post to Ledger"]:::workflow
                WF_VOUCHER_PRINT["5. Print Voucher"]:::workflow
            end

            %% Invoice Workflow
            subgraph INVOICE_WORKFLOW["📄 Invoice Workflow"]
                WF_INVOICE_CREATE["1. Create Invoice"]:::workflow
                WF_INVOICE_CALCULATE["2. Calculate Taxes"]:::workflow
                WF_INVOICE_APPROVE["3. Approve Invoice"]:::workflow
                WF_INVOICE_GENERATE["4. Generate Invoice"]:::workflow
                WF_INVOICE_DISPATCH["5. Dispatch Invoice"]:::workflow
            end

            %% Bill Booking Workflow
            subgraph BILL_WORKFLOW["📑 Bill Booking Workflow"]
                WF_BILL_CREATE["1. Create Bill"]:::workflow
                WF_BILL_VERIFY["2. Verify Details"]:::workflow
                WF_BILL_AUTHORIZE["3. Authorize Bill"]:::workflow
                WF_BILL_APPROVE["4. Approve Bill"]:::workflow
                WF_BILL_PAYMENT["5. Process Payment"]:::workflow
            end

            %% Financial Workflow
            subgraph FINANCIAL_WORKFLOW["💼 Financial Workflow"]
                WF_ASSET_REGISTER["1. Register Asset"]:::workflow
                WF_DEPRECIATION["2. Calculate Depreciation"]:::workflow
                WF_CAPITAL_MANAGE["3. Manage Capital"]:::workflow
                WF_LOAN_PROCESS["4. Process Loans"]:::workflow
                WF_FINANCIAL_REPORT["5. Generate Reports"]:::workflow
            end
        end

        %% Integration Points
        subgraph INTEGRATIONS["🔗 Integration Points"]
            direction TB

            %% Module Integrations
            INT_INVENTORY["Inventory Module<br/>Material Cost Integration"]:::integration
            INT_PURCHASE["Purchase Module<br/>Supplier & PO Integration"]:::integration
            INT_SALES["Sales Module<br/>Customer & Invoice Integration"]:::integration
            INT_HR["HR Module<br/>Employee & Payroll"]:::integration
            INT_PROJECT["Project Module<br/>Project Cost Integration"]:::integration
            INT_MIS["MIS Module<br/>Budget & Financial Analysis"]:::integration
            INT_QUALITY["Quality Module<br/>Quality Cost Integration"]:::integration
            INT_PRODUCTION["Production Module<br/>Production Cost"]:::integration

            %% External Integrations
            INT_BANK_API["Bank API<br/>Banking Integration"]:::integration
            INT_TAX_PORTAL["Tax Portal<br/>Government Tax Filing"]:::integration
            INT_AUDIT_SYSTEM["Audit System<br/>External Audit"]:::integration
            INT_ERP_MODULES["Other ERP Modules<br/>Cross-Module Integration"]:::integration
        end
    end

    %% Main Flow Connections
    ACC_DASHBOARD --> ASP_MAIN_DASH
    ACC_DASHBOARD --> DJANGO_DASH

    %% ASP.NET Flow
    ASP_MAIN_DASH --> MASTERS
    ASP_MAIN_DASH --> TRANSACTIONS
    ASP_MAIN_DASH --> REPORTS

    %% Masters Flow
    MASTERS_DASH --> ACCOUNT_SETUP
    MASTERS_DASH --> TAX_SETUP
    MASTERS_DASH --> FINANCIAL_SETUP
    MASTERS_DASH --> OTHER_MASTERS

    %% Account Setup Flow
    ACC_HEAD --> TBL_ACC_HEAD
    BANK_MASTER --> TBL_BANK_MASTER
    CURRENCY --> TBL_CURRENCY
    PAYMENT_MODE --> TBL_PAYMENT_MODE
    PAYMENT_TERMS --> TBL_PAYMENT_TERMS
    PAID_TYPE --> TBL_PAID_TYPE
    CHEQUE_SERIES --> TBL_CHEQUE_SERIES

    %% Tax Setup Flow
    VAT_MASTER --> TBL_VAT
    EXCISE_MASTER --> TBL_EXCISE
    EXCISABLE_COMMODITY --> TBL_EXCISABLE_COMMODITY
    TDS_CODE --> TBL_TDS_CODE
    OCTORI --> TBL_OCTORI

    %% Financial Setup Flow
    ASSET_MASTER --> TBL_ASSET
    LOAN_TYPE --> TBL_LOAN_TYPE
    INTEREST_TYPE --> TBL_INTEREST_TYPE
    INVOICE_AGAINST --> TBL_INVOICE_AGAINST
    PAYMENT_RECEIPT_AGAINST --> TBL_PAYMENT_RECEIPT_AGAINST

    %% Other Masters Flow
    FREIGHT --> TBL_FREIGHT
    PACKING_FORWARDING --> TBL_PACKING_FORWARDING
    WARRANTY_TERMS --> TBL_WARRANTY_TERMS
    IOU_REASONS --> TBL_IOU_REASONS
    TOUR_EXPENSES --> TBL_TOUR_EXPENSES

    %% Transactions Flow
    TRANS_DASH --> VOUCHER_MGMT
    TRANS_DASH --> INVOICE_MGMT
    TRANS_DASH --> BILL_MGMT
    TRANS_DASH --> CREDIT_DEBIT_NOTES
    TRANS_DASH --> FINANCIAL_TRANS
    TRANS_DASH --> SUNDRY_MGMT
    TRANS_DASH --> OTHER_TRANS

    %% Voucher Management Flow
    CASH_VOUCHER_NEW --> TBL_CASH_VOUCHER_MASTER
    CASH_VOUCHER_NEW --> TBL_CASH_VOUCHER_DETAILS
    BANK_VOUCHER --> TBL_BANK_VOUCHER_MASTER
    BANK_VOUCHER --> TBL_BANK_VOUCHER_DETAILS
    CONTRA_ENTRY --> TBL_CONTRA_ENTRY_MASTER
    CONTRA_ENTRY --> TBL_CONTRA_ENTRY_DETAILS

    %% Invoice Management Flow
    SALES_INV_NEW --> TBL_SALES_INVOICE_MASTER
    SALES_INV_NEW --> TBL_SALES_INVOICE_DETAILS
    PROFORMA_NEW --> TBL_PROFORMA_INVOICE_MASTER
    PROFORMA_NEW --> TBL_PROFORMA_INVOICE_DETAILS
    SERVICE_TAX_NEW --> TBL_SERVICE_TAX_INVOICE_MASTER
    SERVICE_TAX_NEW --> TBL_SERVICE_TAX_INVOICE_DETAILS

    %% Bill Management Flow
    BILL_BOOKING_NEW --> TBL_BILL_BOOKING_MASTER
    BILL_BOOKING_NEW --> TBL_BILL_BOOKING_DETAILS

    %% Credit/Debit Notes Flow
    CREDIT_NOTE --> TBL_CREDIT_NOTE_MASTER
    CREDIT_NOTE --> TBL_CREDIT_NOTE_DETAILS
    DEBIT_NOTE --> TBL_DEBIT_NOTE_MASTER
    DEBIT_NOTE --> TBL_DEBIT_NOTE_DETAILS

    %% Django Flow
    DJANGO_DASH --> DJANGO_MASTERS
    DJANGO_DASH --> DJANGO_VOUCHERS
    DJANGO_DASH --> DJANGO_INVOICES
    DJANGO_DASH --> DJANGO_BILLS
    DJANGO_DASH --> DJANGO_NOTES
    DJANGO_DASH --> DJANGO_FINANCIAL
    DJANGO_DASH --> DJANGO_SUNDRY
    DJANGO_DASH --> DJANGO_OTHER_TRANS
    DJANGO_DASH --> DJANGO_REPORTING

    %% Django to Database
    ACC_HEAD_LIST --> TBL_ACC_HEAD
    BANK_LIST --> TBL_BANK_MASTER
    VAT_LIST --> TBL_VAT
    CASH_VOUCHER_LIST --> TBL_CASH_VOUCHER_MASTER
    SALES_INV_LIST --> TBL_SALES_INVOICE_MASTER
    BILL_BOOKING_LIST --> TBL_BILL_BOOKING_MASTER

    %% Business Logic Connections
    CASH_VOUCHER_NEW --> VOUCHER_PROCESSOR
    BANK_VOUCHER --> VOUCHER_PROCESSOR
    SALES_INV_NEW --> INVOICE_GENERATOR
    SALES_INV_NEW --> TAX_CALCULATOR
    BILL_BOOKING_NEW --> APPROVAL_ENGINE
    ASSET_REGISTER --> FINANCIAL_CALCULATOR

    %% Django Business Logic
    CASH_VOUCHER_CREATE --> VOUCHER_ENGINE
    SALES_INV_CREATE --> INVOICE_ENGINE
    BILL_BOOKING_CREATE --> APPROVAL_WORKFLOW
    ASSET_CREATE --> FINANCIAL_ENGINE

    %% Workflow Connections
    WF_VOUCHER_CREATE --> WF_VOUCHER_VALIDATE
    WF_VOUCHER_VALIDATE --> WF_VOUCHER_APPROVE
    WF_VOUCHER_APPROVE --> WF_VOUCHER_POST
    WF_VOUCHER_POST --> WF_VOUCHER_PRINT

    WF_INVOICE_CREATE --> WF_INVOICE_CALCULATE
    WF_INVOICE_CALCULATE --> WF_INVOICE_APPROVE
    WF_INVOICE_APPROVE --> WF_INVOICE_GENERATE
    WF_INVOICE_GENERATE --> WF_INVOICE_DISPATCH

    WF_BILL_CREATE --> WF_BILL_VERIFY
    WF_BILL_VERIFY --> WF_BILL_AUTHORIZE
    WF_BILL_AUTHORIZE --> WF_BILL_APPROVE
    WF_BILL_APPROVE --> WF_BILL_PAYMENT

    WF_ASSET_REGISTER --> WF_DEPRECIATION
    WF_DEPRECIATION --> WF_CAPITAL_MANAGE
    WF_CAPITAL_MANAGE --> WF_LOAN_PROCESS
    WF_LOAN_PROCESS --> WF_FINANCIAL_REPORT

    %% Integration Connections
    SALES_INV_NEW --> INT_SALES
    BILL_BOOKING_NEW --> INT_PURCHASE
    ASSET_REGISTER --> INT_INVENTORY
    TOUR_VOUCHER --> INT_HR
    CASH_VOUCHER_NEW --> INT_MIS
    BANK_VOUCHER --> INT_BANK_API
    VAT_REGISTER --> INT_TAX_PORTAL

    %% Reports Flow
    REP_DASH --> FINANCIAL_REPORTS
    REP_DASH --> SEARCH_ANALYSIS
    REP_DASH --> CRYSTAL_REPORTS

    CASH_BANK_REGISTER --> CASH_BANK_REGISTER_RPT
    SALES_REGISTER --> SALES_VAT_RPT
    PURCHASE_REPORT --> PURCHASE_RPT
    VAT_REGISTER --> PURCHASE_VAT_RPT

    %% Cross-System Parity
    ACC_HEAD -.->|"Functional Parity"| ACC_HEAD_LIST
    BANK_MASTER -.->|"Functional Parity"| BANK_LIST
    CURRENCY -.->|"Functional Parity"| CURRENCY_LIST
    VAT_MASTER -.->|"Functional Parity"| VAT_LIST
    EXCISE_MASTER -.->|"Functional Parity"| EXCISE_LIST
    TDS_CODE -.->|"Functional Parity"| TDS_LIST
    CASH_VOUCHER_NEW -.->|"Functional Parity"| CASH_VOUCHER_CREATE
    BANK_VOUCHER -.->|"Functional Parity"| BANK_VOUCHER_CREATE
    CONTRA_ENTRY -.->|"Functional Parity"| CONTRA_ENTRY_CREATE
    SALES_INV_NEW -.->|"Functional Parity"| SALES_INV_CREATE
    PROFORMA_NEW -.->|"Functional Parity"| PROFORMA_CREATE
    SERVICE_TAX_NEW -.->|"Functional Parity"| SERVICE_TAX_CREATE
    BILL_BOOKING_NEW -.->|"Functional Parity"| BILL_BOOKING_CREATE
    CREDIT_NOTE -.->|"Functional Parity"| CREDIT_NOTE_CREATE
    DEBIT_NOTE -.->|"Functional Parity"| DEBIT_NOTE_CREATE
    ASSET_REGISTER -.->|"Functional Parity"| ASSET_CREATE
    CAPITAL -.->|"Functional Parity"| CAPITAL_CREATE
    ACC_LOAN_MASTER -.->|"Functional Parity"| LOAN_CREATE
    SUNDRY_CREDITORS -.->|"Functional Parity"| SUNDRY_CREDITORS_CREATE
    CREDITORS_DEBITORS -.->|"Functional Parity"| CREDITORS_DEBITORS_CREATE
    ADVICE -.->|"Functional Parity"| ADVICE_CREATE
    BANK_RECONCILIATION -.->|"Functional Parity"| BANK_RECONCILIATION_CREATE
    TOUR_VOUCHER -.->|"Functional Parity"| TOUR_VOUCHER_CREATE
    IOU_PAYMENT_RECEIPT -.->|"Functional Parity"| IOU_PAYMENT_CREATE
    BALANCE_SHEET -.->|"Functional Parity"| BALANCE_SHEET_VIEW
    CASH_BANK_REGISTER -.->|"Functional Parity"| CASH_BANK_REGISTER_VIEW
    PURCHASE_REPORT -.->|"Functional Parity"| PURCHASE_REPORT_VIEW
    SALES_REGISTER -.->|"Functional Parity"| SALES_REGISTER_VIEW
    VAT_REGISTER -.->|"Functional Parity"| VAT_REGISTER_VIEW
    SEARCH_REPORT -.->|"Functional Parity"| SEARCH_VIEW

    %% Apply styles
    class ASP_MAIN_DASH,MASTERS_DASH,ACC_HEAD,BANK_MASTER,CURRENCY,PAYMENT_MODE,PAYMENT_TERMS,PAID_TYPE,CHEQUE_SERIES,CASH_BANK_ENTRY,VAT_MASTER,EXCISE_MASTER,EXCISABLE_COMMODITY,TDS_CODE,OCTORI,ASSET_MASTER,LOAN_TYPE,INTEREST_TYPE,INVOICE_AGAINST,PAYMENT_RECEIPT_AGAINST,FREIGHT,PACKING_FORWARDING,WARRANTY_TERMS,IOU_REASONS,TOUR_EXPENSES,MASTERS_CONFIG,TRANS_DASH,CASH_VOUCHER_NEW,CASH_VOUCHER_DELETE,CASH_VOUCHER_PRINT,CASH_VOUCHER_PAY_PRINT,CASH_VOUCHER_REC_PRINT,BANK_VOUCHER,BANK_VOUCHER_DELETE,BANK_VOUCHER_PRINT,BANK_VOUCHER_ADVICE,CONTRA_ENTRY,SALES_INV_DASH,SALES_INV_NEW,SALES_INV_EDIT,SALES_INV_DELETE,SALES_INV_PRINT,PROFORMA_NEW,PROFORMA_EDIT,PROFORMA_DELETE,PROFORMA_PRINT,SERVICE_TAX_DASH,SERVICE_TAX_NEW,SERVICE_TAX_EDIT,SERVICE_TAX_DELETE,SERVICE_TAX_PRINT,BILL_BOOKING_NEW,BILL_BOOKING_EDIT,BILL_BOOKING_DELETE,BILL_BOOKING_AUTHORIZE,BILL_BOOKING_PRINT,BILL_BOOKING_ITEM_GRID,CREDIT_NOTE,DEBIT_NOTE,ASSET_REGISTER,ASSET_REGISTER1,ASSET_REGISTER_REPORT,CAPITAL,ACC_LOAN_MASTER,CURRENT_LIABILITIES,ACC_BAL_CURR_ASSETS,ACC_CAPITAL_PART,ACC_CAPITAL_PART_DET,ACC_LOAN_PART,ACC_LOAN_PART_DET,SUNDRY_CREDITORS,SUNDRY_CREDITORS_DETAILS,SUNDRY_CREDITORS_LIST,SUNDRY_CREDITORS_VIEW,CREDITORS_DEBITORS,CREDITORS_DEBITORS_LIST,CREDITORS_DEBITORS_VIEW,ACC_SUNDRY_CUST_LIST,ACC_SUNDRY_DETAILS,ADVICE,ADVICE_DELETE,ADVICE_PRINT,ADVICE_PRINT_ADVICE,BANK_RECONCILIATION,TOUR_VOUCHER,TOUR_VOUCHER_EDIT,TOUR_VOUCHER_DELETE,TOUR_VOUCHER_PRINT,IOU_PAYMENT_RECEIPT,BALANCE_SHEET,PENDING_INVOICE_PRINT,MAIL_MERGE,REP_DASH,CASH_BANK_REGISTER,PURCHASE_REPORT,SALES_REGISTER,VAT_REGISTER,PURCHASE_VAT_REGISTER,SEARCH_REPORT,SEARCH_DETAILS aspnetPage

    class DJANGO_DASH,ACC_HEAD_LIST,ACC_HEAD_CREATE,ACC_HEAD_UPDATE,ACC_HEAD_DELETE,BANK_LIST,BANK_CREATE,BANK_UPDATE,BANK_DELETE,CURRENCY_LIST,CURRENCY_CREATE,PAYMENT_MODE_LIST,PAYMENT_MODE_CREATE,VAT_LIST,VAT_CREATE,VAT_UPDATE,EXCISE_LIST,EXCISE_CREATE,EXCISE_UPDATE,TDS_LIST,TDS_CREATE,TDS_UPDATE,CASH_VOUCHER_LIST,CASH_VOUCHER_CREATE,CASH_VOUCHER_UPDATE,CASH_VOUCHER_DELETE,CASH_VOUCHER_APPROVAL,BANK_VOUCHER_LIST,BANK_VOUCHER_CREATE,BANK_VOUCHER_UPDATE,BANK_VOUCHER_DELETE,BANK_VOUCHER_APPROVAL,CONTRA_ENTRY_LIST,CONTRA_ENTRY_CREATE,CONTRA_ENTRY_UPDATE,SALES_INV_LIST,SALES_INV_CREATE,SALES_INV_UPDATE,SALES_INV_DELETE,SALES_INV_APPROVAL,PROFORMA_LIST,PROFORMA_CREATE,PROFORMA_UPDATE,PROFORMA_DELETE,SERVICE_TAX_LIST,SERVICE_TAX_CREATE,SERVICE_TAX_UPDATE,SERVICE_TAX_DELETE,BILL_BOOKING_LIST,BILL_BOOKING_CREATE,BILL_BOOKING_UPDATE,BILL_BOOKING_DELETE,BILL_BOOKING_AUTH,BILL_BOOKING_APPROVAL,CREDIT_NOTE_LIST,CREDIT_NOTE_CREATE,CREDIT_NOTE_UPDATE,CREDIT_NOTE_DELETE,DEBIT_NOTE_LIST,DEBIT_NOTE_CREATE,DEBIT_NOTE_UPDATE,DEBIT_NOTE_DELETE,ASSET_LIST,ASSET_CREATE,ASSET_UPDATE,ASSET_DELETE,ASSET_REGISTER_VIEW,CAPITAL_LIST,CAPITAL_CREATE,CAPITAL_UPDATE,LOAN_LIST,LOAN_CREATE,LOAN_UPDATE,CURRENT_LIABILITIES_LIST,CURRENT_ASSETS_LIST,SUNDRY_CREDITORS_LIST,SUNDRY_CREDITORS_CREATE,SUNDRY_CREDITORS_UPDATE,SUNDRY_CREDITORS_DELETE,CREDITORS_DEBITORS_LIST,CREDITORS_DEBITORS_CREATE,CREDITORS_DEBITORS_UPDATE,SUNDRY_CUSTOMER_LIST,SUNDRY_DETAILS_VIEW,ADVICE_LIST,ADVICE_CREATE,ADVICE_UPDATE,ADVICE_DELETE,BANK_RECONCILIATION_LIST,BANK_RECONCILIATION_CREATE,TOUR_VOUCHER_LIST,TOUR_VOUCHER_CREATE,TOUR_VOUCHER_UPDATE,TOUR_VOUCHER_DELETE,IOU_PAYMENT_LIST,IOU_PAYMENT_CREATE,BALANCE_SHEET_VIEW,PENDING_INVOICE_VIEW,MAIL_MERGE_VIEW,ACCOUNTS_REPORTS_DASH,FINANCIAL_REPORTS_VIEW,CASH_BANK_REGISTER_VIEW,PURCHASE_REPORT_VIEW,SALES_REGISTER_VIEW,VAT_REGISTER_VIEW,SEARCH_VIEW djangoView

    class TBL_ACC_HEAD,TBL_BANK_MASTER,TBL_CURRENCY,TBL_PAYMENT_MODE,TBL_PAYMENT_TERMS,TBL_PAID_TYPE,TBL_CHEQUE_SERIES,TBL_VAT,TBL_EXCISE,TBL_EXCISABLE_COMMODITY,TBL_TDS_CODE,TBL_OCTORI,TBL_ASSET,TBL_LOAN_TYPE,TBL_INTEREST_TYPE,TBL_INVOICE_AGAINST,TBL_PAYMENT_RECEIPT_AGAINST,TBL_FREIGHT,TBL_PACKING_FORWARDING,TBL_WARRANTY_TERMS,TBL_IOU_REASONS,TBL_TOUR_EXPENSES,TBL_CASH_VOUCHER_MASTER,TBL_CASH_VOUCHER_DETAILS,TBL_BANK_VOUCHER_MASTER,TBL_BANK_VOUCHER_DETAILS,TBL_CONTRA_ENTRY_MASTER,TBL_CONTRA_ENTRY_DETAILS,TBL_SALES_INVOICE_MASTER,TBL_SALES_INVOICE_DETAILS,TBL_PROFORMA_INVOICE_MASTER,TBL_PROFORMA_INVOICE_DETAILS,TBL_SERVICE_TAX_INVOICE_MASTER,TBL_SERVICE_TAX_INVOICE_DETAILS,TBL_BILL_BOOKING_MASTER,TBL_BILL_BOOKING_DETAILS,TBL_CREDIT_NOTE_MASTER,TBL_CREDIT_NOTE_DETAILS,TBL_DEBIT_NOTE_MASTER,TBL_DEBIT_NOTE_DETAILS,TBL_COMPANY,TBL_FINANCIAL_YEAR,TBL_HR_STAFF,TBL_ITEM_MASTER,TBL_SUPPLIER_MASTER,TBL_CUSTOMER_MASTER database

    class CLS_FUNCTIONS,VOUCHER_PROCESSOR,INVOICE_GENERATOR,TAX_CALCULATOR,FINANCIAL_CALCULATOR,REPORT_GENERATOR,EXPORT_EXCEL,APPROVAL_ENGINE,BANK_INTEGRATION,MAIL_MERGE_ENGINE,ACCOUNTS_FORMS,ACCOUNTS_MODELS,VOUCHER_ENGINE,INVOICE_ENGINE,TAX_ENGINE,FINANCIAL_ENGINE,APPROVAL_WORKFLOW,REPORTING_ENGINE,INTEGRATION_ENGINE,AUDIT_ENGINE businessLogic

    class CASH_VOUCHER_PAYMENT_RPT,CASH_VOUCHER_RECEIPT_RPT,BANK_VOUCHER_PAYMENT_RPT,BANK_VOUCHER_AXIS_RPT,BANK_VOUCHER_DENA_RPT,BANK_VOUCHER_IDBI_RPT,BANK_VOUCHER_ADVICE_RPT,SALES_INVOICE_RPT,PROFORMA_INVOICE_RPT,SERVICE_TAX_INVOICE_RPT,CASH_BANK_REGISTER_RPT,SALES_VAT_RPT,PURCHASE_VAT_RPT,PURCHASE_RPT,PURCHASE_EXCISE_RPT,SALES_EX_PRINT_RPT,SALES_EXCISE_PRINT_RPT,BILL_BOOKING_RPT,ASSET_REGISTER_RPT,ADVICE_PRINT_RPT,ADVICE_PRINT_ADVICE_RPT,PENDING_INVOICE_RPT,SUNDRY_CREDITORS_RPT,CREDITORS_DEBITORS_RPT,ACC_SUNDRY_DR_RPT report

    class WF_VOUCHER_CREATE,WF_VOUCHER_VALIDATE,WF_VOUCHER_APPROVE,WF_VOUCHER_POST,WF_VOUCHER_PRINT,WF_INVOICE_CREATE,WF_INVOICE_CALCULATE,WF_INVOICE_APPROVE,WF_INVOICE_GENERATE,WF_INVOICE_DISPATCH,WF_BILL_CREATE,WF_BILL_VERIFY,WF_BILL_AUTHORIZE,WF_BILL_APPROVE,WF_BILL_PAYMENT,WF_ASSET_REGISTER,WF_DEPRECIATION,WF_CAPITAL_MANAGE,WF_LOAN_PROCESS,WF_FINANCIAL_REPORT workflow

    class INT_INVENTORY,INT_PURCHASE,INT_SALES,INT_HR,INT_PROJECT,INT_MIS,INT_QUALITY,INT_PRODUCTION,INT_BANK_API,INT_TAX_PORTAL,INT_AUDIT_SYSTEM,INT_ERP_MODULES integration
