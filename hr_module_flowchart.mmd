graph TB
    %% Color definitions
    classDef aspnetPage fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef djangoView fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef database fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    classDef businessLogic fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef report fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    classDef workflow fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    classDef integration fill:#fce4ec,stroke:#ad1457,stroke-width:2px,color:#000

    %% Main HR Module Structure
    subgraph HR_MODULE["👥 HR Module - Human Resource Management System"]
        direction TB
        
        %% Entry Points
        HR_DASHBOARD[("📊 HR Dashboard<br/>Entry Point")]
        
        %% ASP.NET Structure
        subgraph ASP_NET["🌐 ASP.NET NewERP Structure"]
            direction TB
            
            %% Main Dashboard
            ASP_MAIN_DASH["Dashboard.aspx<br/>Main HR Dashboard"]:::aspnetPage
            
            %% Masters Section
            subgraph MASTERS["📋 Masters Section"]
                direction TB
                MASTERS_DASH["Masters/Dashboard.aspx<br/>Masters Menu"]:::aspnetPage
                
                %% Employee Management
                subgraph EMPLOYEE_MGMT["👤 Employee Management"]
                    EMP_MASTER["Employee_Master.aspx<br/>Employee Master"]:::aspnetPage
                    EMP_CATEGORY["Employee_Category.aspx<br/>Employee Category"]:::aspnetPage
                    DESIGNATION["Designation.aspx<br/>Designation Master"]:::aspnetPage
                    DEPARTMENT["Department.aspx<br/>Department Master"]:::aspnetPage
                    GRADE["Grade.aspx<br/>Grade Master"]:::aspnetPage
                    SHIFT["Shift.aspx<br/>Shift Master"]:::aspnetPage
                end
                
                %% Payroll Setup
                subgraph PAYROLL_SETUP["💰 Payroll Setup"]
                    SALARY_HEAD["Salary_Head.aspx<br/>Salary Head Master"]:::aspnetPage
                    ALLOWANCE["Allowance.aspx<br/>Allowance Master"]:::aspnetPage
                    DEDUCTION["Deduction.aspx<br/>Deduction Master"]:::aspnetPage
                    OVERTIME_RATE["Overtime_Rate.aspx<br/>Overtime Rate"]:::aspnetPage
                    LEAVE_TYPE["Leave_Type.aspx<br/>Leave Type Master"]:::aspnetPage
                    HOLIDAY["Holiday.aspx<br/>Holiday Master"]:::aspnetPage
                end
                
                %% Attendance Setup
                subgraph ATTENDANCE_SETUP["⏰ Attendance Setup"]
                    ATTENDANCE_POLICY["Attendance_Policy.aspx<br/>Attendance Policy"]:::aspnetPage
                    WORKING_HOURS["Working_Hours.aspx<br/>Working Hours"]:::aspnetPage
                    BREAK_TIME["Break_Time.aspx<br/>Break Time"]:::aspnetPage
                end
                
                MASTERS_CONFIG["Masters/Web.config<br/>Role: HR_Master"]:::aspnetPage
            end
            
            %% Transactions Section
            subgraph TRANSACTIONS["💼 Transactions Section"]
                direction TB
                TRANS_DASH["Transactions/Dashboard.aspx<br/>Transaction Menu"]:::aspnetPage
                
                %% Employee Transactions
                subgraph EMP_TRANS["👤 Employee Transactions"]
                    EMP_REGISTRATION["Employee_Registration.aspx<br/>Employee Registration"]:::aspnetPage
                    EMP_PROFILE_UPDATE["Employee_Profile_Update.aspx<br/>Profile Update"]:::aspnetPage
                    EMP_TRANSFER["Employee_Transfer.aspx<br/>Employee Transfer"]:::aspnetPage
                    EMP_PROMOTION["Employee_Promotion.aspx<br/>Employee Promotion"]:::aspnetPage
                    EMP_RESIGNATION["Employee_Resignation.aspx<br/>Employee Resignation"]:::aspnetPage
                    EMP_TERMINATION["Employee_Termination.aspx<br/>Employee Termination"]:::aspnetPage
                end
                
                %% Attendance Management
                subgraph ATTENDANCE_MGMT["⏰ Attendance Management"]
                    DAILY_ATTENDANCE["Daily_Attendance.aspx<br/>Daily Attendance"]:::aspnetPage
                    ATTENDANCE_ENTRY["Attendance_Entry.aspx<br/>Attendance Entry"]:::aspnetPage
                    ATTENDANCE_CORRECTION["Attendance_Correction.aspx<br/>Attendance Correction"]:::aspnetPage
                    OVERTIME_ENTRY["Overtime_Entry.aspx<br/>Overtime Entry"]:::aspnetPage
                    SHIFT_ASSIGNMENT["Shift_Assignment.aspx<br/>Shift Assignment"]:::aspnetPage
                    BIOMETRIC_SYNC["Biometric_Sync.aspx<br/>Biometric Sync"]:::aspnetPage
                end
                
                %% Leave Management
                subgraph LEAVE_MGMT["🏖️ Leave Management"]
                    LEAVE_APPLICATION["Leave_Application.aspx<br/>Leave Application"]:::aspnetPage
                    LEAVE_APPROVAL["Leave_Approval.aspx<br/>Leave Approval"]:::aspnetPage
                    LEAVE_BALANCE["Leave_Balance.aspx<br/>Leave Balance"]:::aspnetPage
                    LEAVE_ENCASHMENT["Leave_Encashment.aspx<br/>Leave Encashment"]:::aspnetPage
                    COMP_OFF["Comp_Off.aspx<br/>Compensatory Off"]:::aspnetPage
                end
                
                %% Payroll Processing
                subgraph PAYROLL_PROC["💰 Payroll Processing"]
                    SALARY_STRUCTURE["Salary_Structure.aspx<br/>Salary Structure"]:::aspnetPage
                    SALARY_PROCESSING["Salary_Processing.aspx<br/>Salary Processing"]:::aspnetPage
                    SALARY_REVISION["Salary_Revision.aspx<br/>Salary Revision"]:::aspnetPage
                    BONUS_PROCESSING["Bonus_Processing.aspx<br/>Bonus Processing"]:::aspnetPage
                    INCENTIVE_CALC["Incentive_Calculation.aspx<br/>Incentive Calculation"]:::aspnetPage
                    LOAN_MANAGEMENT["Loan_Management.aspx<br/>Employee Loan"]:::aspnetPage
                    ADVANCE_SALARY["Advance_Salary.aspx<br/>Advance Salary"]:::aspnetPage
                end
                
                %% Performance Management
                subgraph PERFORMANCE_MGMT["📈 Performance Management"]
                    PERFORMANCE_APPRAISAL["Performance_Appraisal.aspx<br/>Performance Appraisal"]:::aspnetPage
                    KPI_SETTING["KPI_Setting.aspx<br/>KPI Setting"]:::aspnetPage
                    GOAL_SETTING["Goal_Setting.aspx<br/>Goal Setting"]:::aspnetPage
                    FEEDBACK["Feedback.aspx<br/>Employee Feedback"]:::aspnetPage
                end
                
                %% Training & Development
                subgraph TRAINING_DEV["🎓 Training & Development"]
                    TRAINING_PROGRAM["Training_Program.aspx<br/>Training Program"]:::aspnetPage
                    TRAINING_SCHEDULE["Training_Schedule.aspx<br/>Training Schedule"]:::aspnetPage
                    TRAINING_ATTENDANCE["Training_Attendance.aspx<br/>Training Attendance"]:::aspnetPage
                    SKILL_ASSESSMENT["Skill_Assessment.aspx<br/>Skill Assessment"]:::aspnetPage
                    CERTIFICATION["Certification.aspx<br/>Certification"]:::aspnetPage
                end
            end
            
            %% Reports Section
            subgraph REPORTS["📊 Reports Section"]
                direction TB
                REP_DASH["Reports/Dashboard.aspx<br/>Reports Menu"]:::aspnetPage
                
                %% Employee Reports
                subgraph EMP_REPORTS["👤 Employee Reports"]
                    EMP_LIST_REPORT["Employee_List_Report.aspx<br/>Employee List"]:::aspnetPage
                    EMP_PROFILE_REPORT["Employee_Profile_Report.aspx<br/>Employee Profile"]:::aspnetPage
                    EMP_JOINING_REPORT["Employee_Joining_Report.aspx<br/>Joining Report"]:::aspnetPage
                    EMP_LEAVING_REPORT["Employee_Leaving_Report.aspx<br/>Leaving Report"]:::aspnetPage
                end
                
                %% Attendance Reports
                subgraph ATT_REPORTS["⏰ Attendance Reports"]
                    DAILY_ATT_REPORT["Daily_Attendance_Report.aspx<br/>Daily Attendance"]:::aspnetPage
                    MONTHLY_ATT_REPORT["Monthly_Attendance_Report.aspx<br/>Monthly Attendance"]:::aspnetPage
                    OVERTIME_REPORT["Overtime_Report.aspx<br/>Overtime Report"]:::aspnetPage
                    LATE_COMING_REPORT["Late_Coming_Report.aspx<br/>Late Coming"]:::aspnetPage
                    ABSENT_REPORT["Absent_Report.aspx<br/>Absent Report"]:::aspnetPage
                end
                
                %% Payroll Reports
                subgraph PAYROLL_REPORTS["💰 Payroll Reports"]
                    SALARY_SLIP["Salary_Slip.aspx<br/>Salary Slip"]:::aspnetPage
                    PAYROLL_SUMMARY["Payroll_Summary.aspx<br/>Payroll Summary"]:::aspnetPage
                    PF_REPORT["PF_Report.aspx<br/>PF Report"]:::aspnetPage
                    ESI_REPORT["ESI_Report.aspx<br/>ESI Report"]:::aspnetPage
                    TAX_DEDUCTION["Tax_Deduction_Report.aspx<br/>Tax Deduction"]:::aspnetPage
                    BONUS_REPORT["Bonus_Report.aspx<br/>Bonus Report"]:::aspnetPage
                end
                
                %% Leave Reports
                subgraph LEAVE_REPORTS["🏖️ Leave Reports"]
                    LEAVE_BALANCE_REPORT["Leave_Balance_Report.aspx<br/>Leave Balance"]:::aspnetPage
                    LEAVE_TAKEN_REPORT["Leave_Taken_Report.aspx<br/>Leave Taken"]:::aspnetPage
                    LEAVE_PENDING_REPORT["Leave_Pending_Report.aspx<br/>Pending Leaves"]:::aspnetPage
                end
                
                %% Crystal Reports
                subgraph CRYSTAL_REPORTS["💎 Crystal Reports"]
                    EMP_LIST_RPT["Employee_List.rpt<br/>Employee List Report"]:::report
                    SALARY_SLIP_RPT["Salary_Slip.rpt<br/>Salary Slip Report"]:::report
                    ATT_SUMMARY_RPT["Attendance_Summary.rpt<br/>Attendance Summary"]:::report
                    PAYROLL_REGISTER_RPT["Payroll_Register.rpt<br/>Payroll Register"]:::report
                    LEAVE_REGISTER_RPT["Leave_Register.rpt<br/>Leave Register"]:::report
                end
            end
        end
        
        %% Django Cortex Structure
        subgraph DJANGO_CORTEX["🐍 Django Cortex Structure"]
            direction TB
            
            %% Django Dashboard
            DJANGO_DASH["HRDashboardView<br/>Main Dashboard"]:::djangoView
            
            %% Employee Management
            subgraph DJANGO_EMP["👤 Employee Management"]
                EMP_LIST["EmployeeListView<br/>Employee List"]:::djangoView
                EMP_CREATE["EmployeeCreateView<br/>Create Employee"]:::djangoView
                EMP_UPDATE["EmployeeUpdateView<br/>Update Employee"]:::djangoView
                EMP_DELETE["EmployeeDeleteView<br/>Delete Employee"]:::djangoView
                EMP_DETAIL["EmployeeDetailView<br/>Employee Details"]:::djangoView
                DEPT_LIST["DepartmentListView<br/>Department List"]:::djangoView
                DESIGNATION_LIST["DesignationListView<br/>Designation List"]:::djangoView
            end
            
            %% Attendance Management
            subgraph DJANGO_ATT["⏰ Attendance Management"]
                ATT_LIST["AttendanceListView<br/>Attendance List"]:::djangoView
                ATT_CREATE["AttendanceCreateView<br/>Mark Attendance"]:::djangoView
                ATT_UPDATE["AttendanceUpdateView<br/>Update Attendance"]:::djangoView
                ATT_BULK_ENTRY["AttendanceBulkEntryView<br/>Bulk Entry"]:::djangoView
                ATT_DASHBOARD["AttendanceDashboardView<br/>Attendance Dashboard"]:::djangoView
            end
            
            %% Leave Management
            subgraph DJANGO_LEAVE["🏖️ Leave Management"]
                LEAVE_APP_LIST["LeaveApplicationListView<br/>Leave Applications"]:::djangoView
                LEAVE_APP_CREATE["LeaveApplicationCreateView<br/>Apply Leave"]:::djangoView
                LEAVE_APP_UPDATE["LeaveApplicationUpdateView<br/>Update Leave"]:::djangoView
                LEAVE_APPROVAL_VIEW["LeaveApprovalView<br/>Leave Approval"]:::djangoView
                LEAVE_BALANCE_VIEW["LeaveBalanceView<br/>Leave Balance"]:::djangoView
            end
            
            %% Payroll Management
            subgraph DJANGO_PAYROLL["💰 Payroll Management"]
                SALARY_STRUCT_LIST["SalaryStructureListView<br/>Salary Structures"]:::djangoView
                SALARY_STRUCT_CREATE["SalaryStructureCreateView<br/>Create Structure"]:::djangoView
                PAYROLL_PROCESS["PayrollProcessView<br/>Process Payroll"]:::djangoView
                SALARY_SLIP_VIEW["SalarySlipView<br/>Salary Slip"]:::djangoView
                PAYROLL_DASHBOARD["PayrollDashboardView<br/>Payroll Dashboard"]:::djangoView
            end
            
            %% Reporting
            subgraph DJANGO_REPORTING["📊 HR Reporting"]
                HR_REPORTS_DASH["HRReportsDashboardView<br/>Reports Dashboard"]:::djangoView
                EMP_REPORT_VIEW["EmployeeReportView<br/>Employee Reports"]:::djangoView
                ATT_REPORT_VIEW["AttendanceReportView<br/>Attendance Reports"]:::djangoView
                PAYROLL_REPORT_VIEW["PayrollReportView<br/>Payroll Reports"]:::djangoView
                LEAVE_REPORT_VIEW["LeaveReportView<br/>Leave Reports"]:::djangoView
            end
        end
