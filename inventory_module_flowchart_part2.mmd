        
        %% Database Layer
        subgraph DATABASE["🗄️ Database Layer"]
            direction TB
            
            %% Core Inventory Tables
            subgraph CORE_INV_TABLES["📊 Core Inventory Tables"]
                TBL_ITEM_LOCATION["tblInv_ItemLocation<br/>Item Location Master"]:::database
                TBL_WIS_TIME["tblInv_WIS_Time_Set<br/>WIS Time Configuration"]:::database
                TBL_CLOSING_STOCK["tblInv_ClosingStock<br/>Closing Stock Data"]:::database
            end
            
            %% Transaction Tables
            subgraph TRANSACTION_TABLES["💼 Transaction Tables"]
                %% MRS Tables
                TBL_MRS_MASTER["tblInv_MaterialRequisition_Master<br/>MRS Master"]:::database
                TBL_MRS_DETAILS["tblInv_MaterialRequisition_Details<br/>MRS Details"]:::database
                
                %% MIN Tables
                TBL_MIN_MASTER["tblInv_MaterialIssue_Master<br/>MIN Master"]:::database
                TBL_MIN_DETAILS["tblInv_MaterialIssue_Details<br/>MIN Details"]:::database
                
                %% MRN Tables
                TBL_MRN_MASTER["tblInv_MaterialReturn_Master<br/>MRN Master"]:::database
                TBL_MRN_DETAILS["tblInv_MaterialReturn_Details<br/>MRN Details"]:::database
                
                %% GIN Tables
                TBL_GIN_MASTER["tblInv_Inward_Master<br/>GIN Master"]:::database
                TBL_GIN_DETAILS["tblInv_Inward_Details<br/>GIN Details"]:::database
                
                %% GRR Tables
                TBL_GRR_MASTER["tblinv_MaterialReceived_Master<br/>GRR Master"]:::database
                TBL_GRR_DETAILS["tblinv_MaterialReceived_Details<br/>GRR Details"]:::database
                
                %% MCN Tables
                TBL_MCN_MASTER["tblInv_MaterialCreditNote_Master<br/>MCN Master"]:::database
                TBL_MCN_DETAILS["tblInv_MaterialCreditNote_Details<br/>MCN Details"]:::database
                
                %% Service Note Tables
                TBL_SERVICE_MASTER["tblInv_ServiceNote_Master<br/>Service Note Master"]:::database
                TBL_SERVICE_DETAILS["tblInv_ServiceNote_Details<br/>Service Note Details"]:::database
                
                %% Challan Tables
                TBL_CUST_CHALLAN_MASTER["tblInv_CustomerChallan_Master<br/>Customer Challan Master"]:::database
                TBL_CUST_CHALLAN_DETAILS["tblInv_CustomerChallan_Details<br/>Customer Challan Details"]:::database
                TBL_SUP_CHALLAN_MASTER["tblInv_SupplierChallan_Master<br/>Supplier Challan Master"]:::database
                TBL_SUP_CHALLAN_DETAILS["tblInv_SupplierChallan_Details<br/>Supplier Challan Details"]:::database
                
                %% WIS Tables
                TBL_WIS_MASTER["tblInv_WIS_Master<br/>WIS Master"]:::database
                TBL_WIS_ACTUAL_MAT["tblInv_WIS_ActualRun_Material<br/>WIS Actual Material"]:::database
                TBL_WIS_ACTUAL_ASM["tblInv_WIS_ActualRun_Assembly<br/>WIS Actual Assembly"]:::database
                TBL_WIS_DRY_RUN["tblInv_WIS_DryRun<br/>WIS Dry Run"]:::database
                TBL_WIS_RELEASE["tblInv_WIS_Release_Details<br/>WIS Release Details"]:::database
            end
            
            %% Integration Tables
            subgraph INTEGRATION_TABLES["🔗 Integration Tables"]
                TBL_COMPANY["tblCompany_master<br/>Company Information"]:::database
                TBL_FINANCIAL["tblFinancial_master<br/>Financial Years"]:::database
                TBL_HR_STAFF["tblHR_OfficeStaff<br/>Employee Data"]:::database
                TBL_ITEM_MASTER["tblInv_Item_Master<br/>Item Master"]:::database
                TBL_SUPPLIER_MASTER["tblPur_Supplier_Master<br/>Supplier Master"]:::database
                TBL_CUSTOMER_MASTER["SD_Cust_Master<br/>Customer Master"]:::database
                TBL_PO_MASTER["tblPur_PurchaseOrder_Master<br/>Purchase Order Master"]:::database
                TBL_WO_MASTER["SD_Cust_WorkOrder_Master<br/>Work Order Master"]:::database
            end
        end
        
        %% Business Logic Layer
        subgraph BUSINESS_LOGIC["⚙️ Business Logic Layer"]
            direction TB
            
            %% ASP.NET Business Logic
            subgraph ASP_LOGIC["🌐 ASP.NET Logic"]
                CLS_FUNCTIONS["clsFunctions<br/>Common Functions"]:::businessLogic
                STOCK_CALCULATIONS["Stock Calculation Engine<br/>Stock Balance Logic"]:::businessLogic
                TRANSACTION_PROCESSOR["Transaction Processor<br/>Document Processing"]:::businessLogic
                REPORT_GENERATOR["Report Generator<br/>Crystal Reports Engine"]:::businessLogic
                EXPORT_EXCEL["ExportToExcel<br/>Excel Export"]:::businessLogic
                BARCODE_GENERATOR["Barcode Generator<br/>Label Printing"]:::businessLogic
            end
            
            %% Django Business Logic
            subgraph DJANGO_LOGIC["🐍 Django Logic"]
                INVENTORY_FORMS["Inventory Forms<br/>Validation & Processing"]:::businessLogic
                INVENTORY_MODELS["Inventory Models<br/>Data Management"]:::businessLogic
                STOCK_ENGINE["Stock Engine<br/>Real-time Stock Calculations"]:::businessLogic
                MOVEMENT_TRACKER["Movement Tracker<br/>Inventory Movement Logic"]:::businessLogic
                APPROVAL_ENGINE["Approval Engine<br/>Workflow Management"]:::businessLogic
                ANALYTICS_ENGINE["Analytics Engine<br/>ABC & Movement Analysis"]:::businessLogic
            end
        end
        
        %% Workflow Processes
        subgraph WORKFLOWS["🔄 Workflow Processes"]
            direction TB
            
            %% Inward Material Workflow
            subgraph INWARD_WORKFLOW["📥 Inward Material Workflow"]
                WF_SUPPLIER_CHALLAN["1. Supplier Challan"]:::workflow
                WF_GIN_CREATE["2. Create GIN"]:::workflow
                WF_QUALITY_CHECK["3. Quality Check"]:::workflow
                WF_GRR_CREATE["4. Create GRR"]:::workflow
                WF_STOCK_UPDATE["5. Update Stock"]:::workflow
            end
            
            %% Outward Material Workflow
            subgraph OUTWARD_WORKFLOW["📤 Outward Material Workflow"]
                WF_MRS_REQUEST["1. MRS Request"]:::workflow
                WF_MRS_APPROVAL["2. MRS Approval"]:::workflow
                WF_MIN_ISSUE["3. MIN Issue"]:::workflow
                WF_MATERIAL_DISPATCH["4. Material Dispatch"]:::workflow
                WF_STOCK_DEDUCTION["5. Stock Deduction"]:::workflow
            end
            
            %% Return Workflow
            subgraph RETURN_WORKFLOW["🔄 Return Workflow"]
                WF_RETURN_REQUEST["1. Return Request"]:::workflow
                WF_MRN_CREATE["2. Create MRN"]:::workflow
                WF_QUALITY_INSPECT["3. Quality Inspection"]:::workflow
                WF_STOCK_RETURN["4. Stock Return"]:::workflow
                WF_MCN_PROCESS["5. MCN Processing"]:::workflow
            end
            
            %% WIS Workflow
            subgraph WIS_WORKFLOW["📋 WIS Workflow"]
                WF_WIS_PLAN["1. WIS Planning"]:::workflow
                WF_DRY_RUN["2. Dry Run"]:::workflow
                WF_ACTUAL_RUN["3. Actual Run"]:::workflow
                WF_MATERIAL_CONSUME["4. Material Consumption"]:::workflow
                WF_WIS_RELEASE["5. WIS Release"]:::workflow
            end
        end
        
        %% Integration Points
        subgraph INTEGRATIONS["🔗 Integration Points"]
            direction TB
            
            %% Module Integrations
            INT_PURCHASE["Purchase Module<br/>PO & Supplier Integration"]:::integration
            INT_SALES["Sales Module<br/>Customer & Order Integration"]:::integration
            INT_PRODUCTION["Production Module<br/>Work Order Integration"]:::integration
            INT_QUALITY["Quality Module<br/>QC & Inspection"]:::integration
            INT_ACCOUNTS["Accounts Module<br/>Financial Integration"]:::integration
            INT_HR["HR Module<br/>Employee & Department"]:::integration
            INT_MATERIAL_PLANNING["Material Planning<br/>MRP Integration"]:::integration
            INT_COSTING["Material Costing<br/>Cost Analysis"]:::integration
        end
    end
    
    %% Main Flow Connections
    INV_DASHBOARD --> ASP_MAIN_DASH
    INV_DASHBOARD --> DJANGO_DASH
    
    %% ASP.NET Flow
    ASP_MAIN_DASH --> MASTERS
    ASP_MAIN_DASH --> TRANSACTIONS
    ASP_MAIN_DASH --> REPORTS
    
    %% Masters Flow
    MASTERS_DASH --> ITEM_LOCATION_NEW
    MASTERS_DASH --> ITEM_LOCATION_EDIT
    MASTERS_DASH --> ITEM_LOCATION_DELETE
    MASTERS_DASH --> AUTO_WIS_TIME
    
    %% Transactions Flow
    TRANS_DASH --> INWARD_TRANS
    TRANS_DASH --> OUTWARD_TRANS
    TRANS_DASH --> CREDIT_SERVICE
    TRANS_DASH --> WIS_SECTION
    TRANS_DASH --> STOCK_MGMT
    
    %% Inward Transactions Flow
    INWARD_DASH --> GIN_SECTION
    INWARD_DASH --> GRR_SECTION
    INWARD_DASH --> SUPPLIER_CHALLAN
    
    %% GIN Flow
    GIN_NEW --> GIN_NEW_PO
    GIN_NEW_PO --> GIN_EDIT
    GIN_EDIT --> GIN_PRINT
    
    %% GRR Flow
    GRR_NEW --> GRR_EDIT
    GRR_EDIT --> GRR_PRINT
    RECEIVED_RECEIPT_DASH --> GRR_NEW
    
    %% Outward Transactions Flow
    MRS_DASH --> MRS_NEW
    MRS_NEW --> MRS_EDIT
    MRS_EDIT --> MRS_PRINT
    
    MIN_DASH --> MIN_NEW
    MIN_NEW --> MIN_EDIT
    MIN_EDIT --> MIN_PRINT
    
    MRN_DASH --> MRN_NEW
    MRN_NEW --> MRN_EDIT
    MRN_EDIT --> MRN_PRINT
    
    %% Database Connections
    GIN_NEW --> TBL_GIN_MASTER
    GIN_NEW_PO --> TBL_GIN_DETAILS
    GRR_NEW --> TBL_GRR_MASTER
    MRS_NEW --> TBL_MRS_MASTER
    MIN_NEW --> TBL_MIN_MASTER
    MRN_NEW --> TBL_MRN_MASTER
    MCN_NEW --> TBL_MCN_MASTER
    GSN_NEW --> TBL_SERVICE_MASTER
    WIS_ACTUAL_MAT --> TBL_WIS_ACTUAL_MAT
    WIS_ACTUAL_ASM --> TBL_WIS_ACTUAL_ASM
    ITEM_LOCATION_NEW --> TBL_ITEM_LOCATION
    AUTO_WIS_TIME --> TBL_WIS_TIME
    
    %% Django Flow
    DJANGO_DASH --> DJANGO_LOCATION
    DJANGO_DASH --> DJANGO_MRS
    DJANGO_DASH --> DJANGO_MIN_MRN
    DJANGO_DASH --> DJANGO_GIN_GRR
    DJANGO_DASH --> DJANGO_MCN_SERVICE
    DJANGO_DASH --> DJANGO_MOVEMENT
    DJANGO_DASH --> DJANGO_REPORTING
    
    %% Django to Database
    ITEM_LOCATION_LIST --> TBL_ITEM_LOCATION
    MRS_LIST --> TBL_MRS_MASTER
    MIN_LIST --> TBL_MIN_MASTER
    GIN_LIST --> TBL_GIN_MASTER
    MCN_LIST --> TBL_MCN_MASTER
    
    %% Business Logic Connections
    GIN_NEW --> STOCK_CALCULATIONS
    GRR_NEW --> TRANSACTION_PROCESSOR
    MRS_NEW --> APPROVAL_ENGINE
    MIN_NEW --> STOCK_ENGINE
    ABC_ANALYSIS --> ANALYTICS_ENGINE
    
    %% Workflow Connections
    WF_SUPPLIER_CHALLAN --> WF_GIN_CREATE
    WF_GIN_CREATE --> WF_QUALITY_CHECK
    WF_QUALITY_CHECK --> WF_GRR_CREATE
    WF_GRR_CREATE --> WF_STOCK_UPDATE
    
    WF_MRS_REQUEST --> WF_MRS_APPROVAL
    WF_MRS_APPROVAL --> WF_MIN_ISSUE
    WF_MIN_ISSUE --> WF_MATERIAL_DISPATCH
    WF_MATERIAL_DISPATCH --> WF_STOCK_DEDUCTION
    
    WF_RETURN_REQUEST --> WF_MRN_CREATE
    WF_MRN_CREATE --> WF_QUALITY_INSPECT
    WF_QUALITY_INSPECT --> WF_STOCK_RETURN
    WF_STOCK_RETURN --> WF_MCN_PROCESS
    
    WF_WIS_PLAN --> WF_DRY_RUN
    WF_DRY_RUN --> WF_ACTUAL_RUN
    WF_ACTUAL_RUN --> WF_MATERIAL_CONSUME
    WF_MATERIAL_CONSUME --> WF_WIS_RELEASE
    
    %% Integration Connections
    GIN_NEW --> INT_PURCHASE
    CUST_CHALLAN_NEW --> INT_SALES
    WIS_ACTUAL_MAT --> INT_PRODUCTION
    GRR_NEW --> INT_QUALITY
    MCN_NEW --> INT_ACCOUNTS
    MRS_NEW --> INT_HR
    
    %% Reports Flow
    REP_DASH --> ANALYSIS_REPORTS
    REP_DASH --> STOCK_REPORTS
    REP_DASH --> WO_REPORTS
    REP_DASH --> SEARCH_REPORTS
    
    ABC_ANALYSIS --> ABC_DETAILS
    ABC_DETAILS --> ABC_RPT
    MOVING_NONMOVING --> MOVING_DETAILS
    MOVING_DETAILS --> MOVING_RPT
    STOCK_LEDGER --> STOCK_LEDGER_DETAILS
    STOCK_LEDGER_DETAILS --> STOCK_LEDGER_RPT
    
    %% Cross-System Parity
    ITEM_LOCATION_NEW -.->|"Functional Parity"| ITEM_LOCATION_CREATE
    MRS_NEW -.->|"Functional Parity"| MRS_CREATE
    MIN_NEW -.->|"Functional Parity"| MIN_CREATE
    GIN_NEW -.->|"Functional Parity"| GIN_CREATE
    GRR_NEW -.->|"Functional Parity"| GRR_CREATE
    MRN_NEW -.->|"Functional Parity"| MRN_CREATE
    MCN_NEW -.->|"Functional Parity"| MCN_CREATE
    ABC_ANALYSIS -.->|"Functional Parity"| ABC_ANALYSIS_VIEW
    STOCK_LEDGER -.->|"Functional Parity"| STOCK_LEDGER_LIST
    
    %% Apply styles
    class ASP_MAIN_DASH,MASTERS_DASH,ITEM_LOCATION_NEW,ITEM_LOCATION_EDIT,ITEM_LOCATION_DELETE,AUTO_WIS_TIME,MASTERS_CONFIG,TRANS_DASH,INWARD_DASH,GIN_NEW,GIN_NEW_PO,GIN_EDIT,GIN_DELETE,GIN_PRINT,GRR_NEW,GRR_EDIT,GRR_DELETE,GRR_PRINT,RECEIVED_RECEIPT_DASH,SUP_CHALLAN_NEW,SUP_CHALLAN_EDIT,SUP_CHALLAN_DELETE,SUP_CHALLAN_CLEAR,SUP_CHALLAN_PRINT,MRS_DASH,MRS_NEW,MRS_EDIT,MRS_DELETE,MRS_PRINT,MIN_DASH,MIN_NEW,MIN_EDIT,MIN_DELETE,MIN_PRINT,MRN_DASH,MRN_NEW,MRN_EDIT,MRN_DELETE,MRN_PRINT,CUST_CHALLAN_NEW,CUST_CHALLAN_EDIT,CUST_CHALLAN_DELETE,CUST_CHALLAN_CLEAR,CUST_CHALLAN_PRINT,MCN_DASH,MCN_NEW,MCN_EDIT,MCN_DELETE,MCN_PRINT,SERVICE_NOTE_DASH,GSN_NEW,GSN_EDIT,GSN_DELETE,GSN_PRINT,WIS_ACTUAL_MAT,WIS_ACTUAL_ASM,WIS_DRY_RUN,WIS_RELEASE,WIS_VIEW_TRANS,WIS_PRINT,WISWONO_PRINT,CLOSING_STOCK,TOTAL_SHORTAGE,TOTAL_ISSUE_SHORT,REP_DASH,ABC_ANALYSIS,ABC_DETAILS,MOVING_NONMOVING,MOVING_DETAILS,STOCK_LEDGER,STOCK_LEDGER_DETAILS,STOCK_LEDGER_PRINT,STOCK_STATEMENT,STOCK_STATEMENT_DETAILS,WO_ISSUE,WO_ISSUE_DETAILS,WO_SHORTAGE_DETAILS,SEARCH_REPORT,SEARCH_DETAILS,SEARCH_MRN,SEARCH_MRS,INWARD_OUTWARD,GENERAL_REPORT aspnetPage
    
    class DJANGO_DASH,ITEM_LOCATION_LIST,ITEM_LOCATION_CREATE,ITEM_LOCATION_UPDATE,ITEM_LOCATION_DELETE,WIS_TIME_CONFIG,MRS_LIST,MRS_TABLE_LIST,MRS_DETAIL,MRS_CREATE,MRS_UPDATE,MRS_DELETE,MRS_APPROVAL,MRS_LINE_ITEM,MIN_LIST,MIN_CREATE,MIN_UPDATE,MIN_DELETE,MRN_LIST,MRN_CREATE,MRN_UPDATE,MRN_DELETE,GIN_LIST,GIN_CREATE,GIN_UPDATE,GIN_DELETE,GRR_LIST,GRR_CREATE,GRR_UPDATE,GRR_DELETE,INWARD_DASHBOARD,MCN_LIST,MCN_CREATE,MCN_UPDATE,MCN_DELETE,MCN_APPROVAL,SERVICE_NOTE_LIST,SERVICE_NOTE_CREATE,SERVICE_NOTE_UPDATE,SERVICE_NOTE_DELETE,MOVEMENT_LIST,MOVEMENT_CREATE,MOVEMENT_UPDATE,MOVEMENT_DELETE,STOCK_LEDGER_LIST,INVENTORY_SNAPSHOT,MOVEMENT_DASHBOARD,REPORTS_DASHBOARD,ABC_ANALYSIS_VIEW,MOVING_ANALYSIS_VIEW,STOCK_STATEMENT_VIEW,UNIVERSAL_SEARCH djangoView
    
    class TBL_ITEM_LOCATION,TBL_WIS_TIME,TBL_CLOSING_STOCK,TBL_MRS_MASTER,TBL_MRS_DETAILS,TBL_MIN_MASTER,TBL_MIN_DETAILS,TBL_MRN_MASTER,TBL_MRN_DETAILS,TBL_GIN_MASTER,TBL_GIN_DETAILS,TBL_GRR_MASTER,TBL_GRR_DETAILS,TBL_MCN_MASTER,TBL_MCN_DETAILS,TBL_SERVICE_MASTER,TBL_SERVICE_DETAILS,TBL_CUST_CHALLAN_MASTER,TBL_CUST_CHALLAN_DETAILS,TBL_SUP_CHALLAN_MASTER,TBL_SUP_CHALLAN_DETAILS,TBL_WIS_MASTER,TBL_WIS_ACTUAL_MAT,TBL_WIS_ACTUAL_ASM,TBL_WIS_DRY_RUN,TBL_WIS_RELEASE,TBL_COMPANY,TBL_FINANCIAL,TBL_HR_STAFF,TBL_ITEM_MASTER,TBL_SUPPLIER_MASTER,TBL_CUSTOMER_MASTER,TBL_PO_MASTER,TBL_WO_MASTER database
    
    class CLS_FUNCTIONS,STOCK_CALCULATIONS,TRANSACTION_PROCESSOR,REPORT_GENERATOR,EXPORT_EXCEL,BARCODE_GENERATOR,INVENTORY_FORMS,INVENTORY_MODELS,STOCK_ENGINE,MOVEMENT_TRACKER,APPROVAL_ENGINE,ANALYTICS_ENGINE businessLogic
    
    class ABC_RPT,MOVING_RPT,STOCK_LEDGER_RPT,STOCK_STATEMENT_RPT,WO_ISSUE_RPT,WO_SHORTAGE_RPT report
    
    class WF_SUPPLIER_CHALLAN,WF_GIN_CREATE,WF_QUALITY_CHECK,WF_GRR_CREATE,WF_STOCK_UPDATE,WF_MRS_REQUEST,WF_MRS_APPROVAL,WF_MIN_ISSUE,WF_MATERIAL_DISPATCH,WF_STOCK_DEDUCTION,WF_RETURN_REQUEST,WF_MRN_CREATE,WF_QUALITY_INSPECT,WF_STOCK_RETURN,WF_MCN_PROCESS,WF_WIS_PLAN,WF_DRY_RUN,WF_ACTUAL_RUN,WF_MATERIAL_CONSUME,WF_WIS_RELEASE workflow
    
    class INT_PURCHASE,INT_SALES,INT_PRODUCTION,INT_QUALITY,INT_ACCOUNTS,INT_HR,INT_MATERIAL_PLANNING,INT_COSTING integration
