graph TB
    %% Color definitions
    classDef aspnetPage fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef djangoView fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef database fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    classDef businessLogic fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef report fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    classDef workflow fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    classDef integration fill:#fce4ec,stroke:#ad1457,stroke-width:2px,color:#000

    %% Main MIS Module Structure
    subgraph MIS_MODULE["🏢 MIS Module - Management Information System"]
        direction TB
        
        %% Entry Points
        MIS_DASHBOARD[("📊 MIS Dashboard<br/>Entry Point")]
        
        %% ASP.NET Structure
        subgraph ASP_NET["🌐 ASP.NET NewERP Structure"]
            direction TB
            
            %% Main Dashboard
            ASP_MAIN_DASH["Dashboard.aspx<br/>Main MIS Dashboard"]:::aspnetPage
            
            %% Masters Section
            subgraph MASTERS["📋 Masters Section"]
                direction TB
                BUDGET_CODE_ASP["Budget_Code.aspx<br/>Budget Code Management<br/>CRUD Operations"]:::aspnetPage
                MASTERS_DASH["Masters/Dashboard.aspx<br/>Masters Menu"]:::aspnetPage
                MASTERS_CONFIG["Masters/Web.config<br/>Role: MIS_Master"]:::aspnetPage
            end
            
            %% Transactions Section
            subgraph TRANSACTIONS["💼 Transactions Section"]
                direction TB
                TRANS_DASH["Transactions/Dashboard.aspx<br/>Transaction Menu"]:::aspnetPage
                TRANS_MENU["Transactions/Menu.aspx<br/>Navigation Hub"]:::aspnetPage
                
                %% Budget Allocation
                subgraph BUDGET_ALLOC["💰 Budget Allocation"]
                    BUDGET_CODE_ALLOC["BudgetCode_Allocation.aspx<br/>Code Assignment"]:::aspnetPage
                    BUDGET_DIST["Budget_Dist.aspx<br/>Distribution Management"]:::aspnetPage
                    BUDGET_DIST_DEPT["Budget_Dist_Dept.aspx<br/>Department Distribution"]:::aspnetPage
                    BUDGET_DIST_DEPT_DET["Budget_Dist_Dept_Details.aspx<br/>Detailed Allocation"]:::aspnetPage
                    BUDGET_DIST_DEPT_TIME["Budget_Dist_Dept_Details_Time.aspx<br/>Time-based Tracking"]:::aspnetPage
                end
                
                %% Work Order Budget
                subgraph WO_BUDGET["🔧 Work Order Budget"]
                    BUDGET_WONO["Budget_WONo.aspx<br/>WO Budget Setup"]:::aspnetPage
                    BUDGET_WONO_DET["Budget_WONo_Details.aspx<br/>WO Budget Details"]:::aspnetPage
                    BUDGET_WONO_TIME["Budget_WONo_Details_Time.aspx<br/>WO Time Tracking"]:::aspnetPage
                    BUDGET_DIST_WONO["Budget_Dist_WONo.aspx<br/>WO Distribution"]:::aspnetPage
                    BUDGET_DIST_WONO_TIME["Budget_Dist_WONo_Time.aspx<br/>WO Time Distribution"]:::aspnetPage
                end
                
                %% Labor & Material Budget
                subgraph LABOR_MAT["👷 Labor & Material"]
                    BUDGET_LABOR["Budget_Labour_Details.aspx<br/>Labor Budget Details"]:::aspnetPage
                    BUDGET_LABOR_PRINT["Budget_Labour_Print.aspx<br/>Labor Reports"]:::aspnetPage
                    BUDGET_WITH_MAT["Budget_WithMaterial_Details.aspx<br/>Material Budget"]:::aspnetPage
                    BUDGET_WITH_MAT_PRINT["Budget_WithMaterial_Print.aspx<br/>Material Reports"]:::aspnetPage
                end
                
                %% Hours & Summary
                subgraph HOURS_SUMMARY["⏱️ Hours & Summary"]
                    BUDGET_HRS_FIELDS["BudgetHrsFields.aspx<br/>Hours Field Management"]:::aspnetPage
                    HRS_BUDGET_SUMMARY["HrsBudgetSummary.aspx<br/>Hours Summary"]:::aspnetPage
                    HRS_BUDGET_EQUIP["HrsBudgetSummary_Equip.aspx<br/>Equipment Hours"]:::aspnetPage
                end
                
                %% Print & Export
                subgraph PRINT_EXPORT["🖨️ Print & Export"]
                    BUDGET_DEPT_PRINT["Budget_Dept_Print.aspx<br/>Department Reports"]:::aspnetPage
                    BUDGET_WO_PRINT["Budget_WO_Print.aspx<br/>Work Order Reports"]:::aspnetPage
                end
            end
            
            %% Reports Section
            subgraph REPORTS["📊 Reports Section"]
                direction TB
                REP_DASH["Reports/Dashboard.aspx<br/>Reports Menu"]:::aspnetPage
                
                %% BOM & Costing
                BOM_COSTING["BOMCosting.aspx<br/>BOM Cost Analysis"]:::aspnetPage
                BOM_COSTING_REP["BOMCosting_Report.aspx<br/>BOM Cost Reports"]:::aspnetPage
                
                %% Tax Reports
                EXCISE_VAT["Excise_VAT_CST_Compute.aspx<br/>Tax Computation"]:::aspnetPage
                SERVICE_TAX["ServiceTaxReport.aspx<br/>Service Tax Reports"]:::aspnetPage
                
                %% Operational Reports
                FINISH_PROC["FinishProcessingReport.aspx<br/>Process Reports"]:::aspnetPage
                PURCHASE_REP["PurchaseReport.aspx<br/>Purchase Analysis"]:::aspnetPage
                SALES_REP["SalesReport.aspx<br/>Sales Analysis"]:::aspnetPage
                SALES_DIST["SalesDistribution.aspx<br/>Distribution Reports"]:::aspnetPage
                QA_POWISE["QA_POwise.aspx<br/>Quality Reports"]:::aspnetPage
                
                %% Crystal Reports
                BOM_PRINT_RPT["BOM_Print_ALL.rpt<br/>BOM Crystal Report"]:::report
                EXCISE_VAT_RPT["Ex_Vat_CST_Computation.rpt<br/>Tax Crystal Report"]:::report
                QA_POWISE_RPT["QA_POWise_Print.rpt<br/>QA Crystal Report"]:::report
            end
        end
        
        %% Django Cortex Structure
        subgraph DJANGO_CORTEX["🐍 Django Cortex Structure"]
            direction TB
            
            %% Django Dashboard
            DJANGO_DASH["BudgetDashboardView<br/>Main Dashboard"]:::djangoView
            
            %% Budget Management
            subgraph DJANGO_BUDGET["💰 Budget Management"]
                BUDGET_CODE_LIST["BudgetCodeListView<br/>List & Search"]:::djangoView
                BUDGET_CODE_CREATE["BudgetCodeCreateView<br/>Create New"]:::djangoView
                BUDGET_CODE_UPDATE["BudgetCodeUpdateView<br/>Edit Existing"]:::djangoView
                BUDGET_CODE_DELETE["BudgetCodeDeleteView<br/>Remove"]:::djangoView
                
                BUDGET_PERIOD_LIST["BudgetPeriodListView<br/>Period Management"]:::djangoView
                BUDGET_ALLOC_LIST["BudgetAllocationListView<br/>Allocation Management"]:::djangoView
                BUDGET_DIST_LIST["BudgetDistributionListView<br/>Distribution Management"]:::djangoView
            end
            
            %% Department Budget
            subgraph DJANGO_DEPT["🏢 Department Budget"]
                DEPT_BUDGET_MASTER["DepartmentBudgetMaster<br/>Department Budgets"]:::djangoView
                DEPT_BUDGET_DETAILS["DepartmentBudgetDetails<br/>Detailed Allocations"]:::djangoView
                DEPT_BUDGET_TIME["DepartmentBudgetTimeTracking<br/>Time-based Tracking"]:::djangoView
                DEPT_BUDGET_TRANSFER["DepartmentBudgetTransfer<br/>Budget Transfers"]:::djangoView
            end
            
            %% Tax Computation
            subgraph DJANGO_TAX["💸 Tax Computation"]
                TAX_CONFIG["TaxConfiguration<br/>Tax Setup"]:::djangoView
                TAX_COMPUTATION["TaxComputation<br/>Tax Calculations"]:::djangoView
                TAX_COMPLIANCE["TaxCompliance<br/>Compliance Management"]:::djangoView
                TAX_REPORTS["TaxReport<br/>Tax Reporting"]:::djangoView
            end

        %% Database Layer
        subgraph DATABASE["🗄️ Database Layer"]
            direction TB

            %% Core MIS Tables
            subgraph CORE_TABLES["📊 Core MIS Tables"]
                TBL_BUDGET_CODE["tblMIS_BudgetCode<br/>Id, Description, Symbol"]:::database
                TBL_BUDGET_CODE_TIME["tblMIS_BudgetCode_Time<br/>Time-based Codes"]:::database
                TBL_BUDGET_HRS_CAT["tblMIS_BudgetHrs_Field_Category<br/>Hours Categories"]:::database
                TBL_BUDGET_HRS_SUBCAT["tblMIS_BudgetHrs_Field_SubCategory<br/>Hours Subcategories"]:::database
            end

            %% Budget Transaction Tables
            subgraph BUDGET_TABLES["💰 Budget Tables"]
                TBL_BUDGET_TRANS["tblACC_Budget_Transactions<br/>Budget Transactions"]:::database
                TBL_BUDGET_DEPT["tblACC_Budget_Dept<br/>Department Budgets"]:::database
                TBL_BUDGET_DEPT_TIME["tblACC_Budget_Dept_Time<br/>Time-based Dept Budget"]:::database
                TBL_BUDGET_WO_TIME["tblACC_Budget_WO_Time<br/>Work Order Time Budget"]:::database
            end

            %% Integration Tables
            subgraph INTEGRATION_TABLES["🔗 Integration Tables"]
                TBL_COMPANY["tblCompany_master<br/>Company Information"]:::database
                TBL_FINANCIAL["tblFinancial_master<br/>Financial Years"]:::database
                TBL_HR_STAFF["tblHR_OfficeStaff<br/>Employee Data"]:::database
                TBL_CUST_WO["SD_Cust_WorkOrder_Master<br/>Work Orders"]:::database
                TBL_PM_MANPOWER["tblPM_ManPowerPlanning<br/>Manpower Planning"]:::database
            end
        end

        %% Business Logic Layer
        subgraph BUSINESS_LOGIC["⚙️ Business Logic Layer"]
            direction TB

            %% ASP.NET Business Logic
            subgraph ASP_LOGIC["🌐 ASP.NET Logic"]
                CLS_FUNCTIONS["clsFunctions<br/>Common Functions"]:::businessLogic
                CAL_BAL_BUDGET["CalBalBudgetAmt<br/>Balance Calculations"]:::businessLogic
                PO_BUDGET_AMT["PO_Budget_Amt<br/>PO Budget Calculations"]:::businessLogic
                EXPORT_EXCEL["ExportToExcel<br/>Excel Export"]:::businessLogic
            end

            %% Django Business Logic
            subgraph DJANGO_LOGIC["🐍 Django Logic"]
                BUDGET_FORMS["Budget Forms<br/>Validation & Processing"]:::businessLogic
                BUDGET_MODELS["Budget Models<br/>Data Management"]:::businessLogic
                BUDGET_UTILS["Budget Utilities<br/>Calculations & Reports"]:::businessLogic
            end
        end

        %% Workflow Processes
        subgraph WORKFLOWS["🔄 Workflow Processes"]
            direction TB

            %% Budget Creation Workflow
            subgraph BUDGET_WORKFLOW["💰 Budget Creation Workflow"]
                WF_CREATE["1. Create Budget Code"]:::workflow
                WF_ALLOCATE["2. Allocate to Department"]:::workflow
                WF_DISTRIBUTE["3. Distribute by Category"]:::workflow
                WF_TRACK["4. Track Utilization"]:::workflow
                WF_REPORT["5. Generate Reports"]:::workflow
            end

            %% Approval Workflow
            subgraph APPROVAL_WORKFLOW["✅ Approval Workflow"]
                WF_DRAFT["Draft Status"]:::workflow
                WF_SUBMIT["Submit for Review"]:::workflow
                WF_APPROVE["Department Approval"]:::workflow
                WF_ACTIVE["Active Budget"]:::workflow
                WF_MONITOR["Monitor & Control"]:::workflow
            end
        end

        %% Integration Points
        subgraph INTEGRATIONS["🔗 Integration Points"]
            direction TB

            %% Module Integrations
            INT_ACCOUNTS["Accounts Module<br/>Financial Integration"]:::integration
            INT_HR["HR Module<br/>Employee & Department"]:::integration
            INT_PROJECT["Project Module<br/>Work Order Integration"]:::integration
            INT_PURCHASE["Purchase Module<br/>PO Budget Tracking"]:::integration
            INT_INVENTORY["Inventory Module<br/>Material Budget"]:::integration
            INT_QUALITY["Quality Module<br/>QA Reports"]:::integration
        end
    end

    %% Main Flow Connections
    MIS_DASHBOARD --> ASP_MAIN_DASH
    MIS_DASHBOARD --> DJANGO_DASH

    %% ASP.NET Flow
    ASP_MAIN_DASH --> MASTERS
    ASP_MAIN_DASH --> TRANSACTIONS
    ASP_MAIN_DASH --> REPORTS

    %% Masters Flow
    MASTERS_DASH --> BUDGET_CODE_ASP
    BUDGET_CODE_ASP --> TBL_BUDGET_CODE

    %% Transactions Flow
    TRANS_DASH --> TRANS_MENU
    TRANS_MENU --> BUDGET_ALLOC
    TRANS_MENU --> WO_BUDGET
    TRANS_MENU --> LABOR_MAT
    TRANS_MENU --> HOURS_SUMMARY

    %% Budget Allocation Flow
    BUDGET_CODE_ALLOC --> BUDGET_DIST
    BUDGET_DIST --> BUDGET_DIST_DEPT
    BUDGET_DIST_DEPT --> BUDGET_DIST_DEPT_DET
    BUDGET_DIST_DEPT_DET --> BUDGET_DIST_DEPT_TIME

    %% Work Order Flow
    BUDGET_WONO --> BUDGET_WONO_DET
    BUDGET_WONO_DET --> BUDGET_WONO_TIME
    BUDGET_DIST_WONO --> BUDGET_DIST_WONO_TIME

    %% Database Connections
    BUDGET_DIST --> TBL_BUDGET_DEPT
    BUDGET_DIST_DEPT_TIME --> TBL_BUDGET_DEPT_TIME
    BUDGET_WONO_TIME --> TBL_BUDGET_WO_TIME
    BUDGET_HRS_FIELDS --> TBL_BUDGET_HRS_CAT
    BUDGET_HRS_FIELDS --> TBL_BUDGET_HRS_SUBCAT

    %% Django Flow
    DJANGO_DASH --> DJANGO_BUDGET
    DJANGO_DASH --> DJANGO_DEPT
    DJANGO_DASH --> DJANGO_TAX

    %% Django to Database
    BUDGET_CODE_LIST --> TBL_BUDGET_CODE
    DEPT_BUDGET_MASTER --> TBL_BUDGET_DEPT
    DEPT_BUDGET_TIME --> TBL_BUDGET_DEPT_TIME

    %% Business Logic Connections
    BUDGET_DIST --> CAL_BAL_BUDGET
    BUDGET_DIST --> PO_BUDGET_AMT
    BOM_COSTING --> CLS_FUNCTIONS

    %% Workflow Connections
    WF_CREATE --> WF_ALLOCATE
    WF_ALLOCATE --> WF_DISTRIBUTE
    WF_DISTRIBUTE --> WF_TRACK
    WF_TRACK --> WF_REPORT

    WF_DRAFT --> WF_SUBMIT
    WF_SUBMIT --> WF_APPROVE
    WF_APPROVE --> WF_ACTIVE
    WF_ACTIVE --> WF_MONITOR

    %% Integration Connections
    BUDGET_DIST --> INT_ACCOUNTS
    BUDGET_DIST_DEPT --> INT_HR
    BUDGET_WONO --> INT_PROJECT
    BOM_COSTING --> INT_PURCHASE
    BUDGET_WITH_MAT --> INT_INVENTORY
    QA_POWISE --> INT_QUALITY

    %% Reports Flow
    REP_DASH --> BOM_COSTING
    REP_DASH --> EXCISE_VAT
    REP_DASH --> PURCHASE_REP
    REP_DASH --> SALES_REP

    BOM_COSTING --> BOM_COSTING_REP
    BOM_COSTING_REP --> BOM_PRINT_RPT
    EXCISE_VAT --> EXCISE_VAT_RPT
    QA_POWISE --> QA_POWISE_RPT

    %% Cross-System Parity
    BUDGET_CODE_ASP -.->|"Functional Parity"| BUDGET_CODE_LIST
    BUDGET_DIST -.->|"Functional Parity"| BUDGET_ALLOC_LIST
    BUDGET_DIST_DEPT -.->|"Functional Parity"| DEPT_BUDGET_MASTER
    BUDGET_DIST_DEPT_TIME -.->|"Functional Parity"| DEPT_BUDGET_TIME
    EXCISE_VAT -.->|"Functional Parity"| TAX_COMPUTATION

    %% Apply styles
    class ASP_MAIN_DASH,BUDGET_CODE_ASP,MASTERS_DASH,TRANS_DASH,TRANS_MENU,BUDGET_CODE_ALLOC,BUDGET_DIST,BUDGET_DIST_DEPT,BUDGET_DIST_DEPT_DET,BUDGET_DIST_DEPT_TIME,BUDGET_WONO,BUDGET_WONO_DET,BUDGET_WONO_TIME,BUDGET_DIST_WONO,BUDGET_DIST_WONO_TIME,BUDGET_LABOR,BUDGET_LABOR_PRINT,BUDGET_WITH_MAT,BUDGET_WITH_MAT_PRINT,BUDGET_HRS_FIELDS,HRS_BUDGET_SUMMARY,HRS_BUDGET_EQUIP,BUDGET_DEPT_PRINT,BUDGET_WO_PRINT,REP_DASH,BOM_COSTING,BOM_COSTING_REP,EXCISE_VAT,SERVICE_TAX,FINISH_PROC,PURCHASE_REP,SALES_REP,SALES_DIST,QA_POWISE,MASTERS_CONFIG aspnetPage

    class DJANGO_DASH,BUDGET_CODE_LIST,BUDGET_CODE_CREATE,BUDGET_CODE_UPDATE,BUDGET_CODE_DELETE,BUDGET_PERIOD_LIST,BUDGET_ALLOC_LIST,BUDGET_DIST_LIST,DEPT_BUDGET_MASTER,DEPT_BUDGET_DETAILS,DEPT_BUDGET_TIME,DEPT_BUDGET_TRANSFER,TAX_CONFIG,TAX_COMPUTATION,TAX_COMPLIANCE,TAX_REPORTS djangoView

    class TBL_BUDGET_CODE,TBL_BUDGET_CODE_TIME,TBL_BUDGET_HRS_CAT,TBL_BUDGET_HRS_SUBCAT,TBL_BUDGET_TRANS,TBL_BUDGET_DEPT,TBL_BUDGET_DEPT_TIME,TBL_BUDGET_WO_TIME,TBL_COMPANY,TBL_FINANCIAL,TBL_HR_STAFF,TBL_CUST_WO,TBL_PM_MANPOWER database

    class CLS_FUNCTIONS,CAL_BAL_BUDGET,PO_BUDGET_AMT,EXPORT_EXCEL,BUDGET_FORMS,BUDGET_MODELS,BUDGET_UTILS businessLogic

    class BOM_PRINT_RPT,EXCISE_VAT_RPT,QA_POWISE_RPT report

    class WF_CREATE,WF_ALLOCATE,WF_DISTRIBUTE,WF_TRACK,WF_REPORT,WF_DRAFT,WF_SUBMIT,WF_APPROVE,WF_ACTIVE,WF_MONITOR workflow

    class INT_ACCOUNTS,INT_HR,INT_PROJECT,INT_PURCHASE,INT_INVENTORY,INT_QUALITY integration
        end
