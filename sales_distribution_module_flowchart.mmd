graph TB
    %% Color definitions
    classDef aspnetPage fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef djangoView fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef database fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    classDef businessLogic fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef report fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    classDef workflow fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    classDef integration fill:#fce4ec,stroke:#ad1457,stroke-width:2px,color:#000

    %% Main Sales Distribution Module Structure
    subgraph SALES_MODULE["🛒 Sales Distribution Module - Customer & Order Management"]
        direction TB
        
        %% Entry Points
        SALES_DASHBOARD[("📊 Sales Dashboard<br/>Entry Point")]
        
        %% ASP.NET Structure
        subgraph ASP_NET["🌐 ASP.NET NewERP Structure"]
            direction TB
            
            %% Main Dashboard
            ASP_MAIN_DASH["Dashboard.aspx<br/>Main Sales Dashboard"]:::aspnetPage
            
            %% Masters Section
            subgraph MASTERS["📋 Masters Section"]
                direction TB
                MASTERS_DASH["Masters/Dashboard.aspx<br/>Masters Menu"]:::aspnetPage
                
                %% Customer Management
                subgraph CUSTOMER_MGMT["👥 Customer Management"]
                    CUSTOMER_MASTER["Customer_Master.aspx<br/>Customer Master"]:::aspnetPage
                    CUSTOMER_CATEGORY["Customer_Category.aspx<br/>Customer Category"]:::aspnetPage
                    CUSTOMER_GROUP["Customer_Group.aspx<br/>Customer Group"]:::aspnetPage
                    CUSTOMER_CONTACT["Customer_Contact.aspx<br/>Customer Contact"]:::aspnetPage
                    CUSTOMER_CREDIT_LIMIT["Customer_Credit_Limit.aspx<br/>Credit Limit"]:::aspnetPage
                end
                
                %% Product Management
                subgraph PRODUCT_MGMT["📦 Product Management"]
                    PRODUCT_MASTER["Product_Master.aspx<br/>Product Master"]:::aspnetPage
                    PRODUCT_CATEGORY["Product_Category.aspx<br/>Product Category"]:::aspnetPage
                    PRICE_LIST["Price_List.aspx<br/>Price List Master"]:::aspnetPage
                    DISCOUNT_SCHEME["Discount_Scheme.aspx<br/>Discount Scheme"]:::aspnetPage
                    PRODUCT_SPECIFICATION["Product_Specification.aspx<br/>Product Specification"]:::aspnetPage
                end
                
                %% Sales Setup
                subgraph SALES_SETUP["⚙️ Sales Setup"]
                    SALES_TERRITORY["Sales_Territory.aspx<br/>Sales Territory"]:::aspnetPage
                    SALES_PERSON["Sales_Person.aspx<br/>Sales Person"]:::aspnetPage
                    COMMISSION_STRUCTURE["Commission_Structure.aspx<br/>Commission Structure"]:::aspnetPage
                    DELIVERY_TERMS["Delivery_Terms.aspx<br/>Delivery Terms"]:::aspnetPage
                    PAYMENT_TERMS_SALES["Payment_Terms.aspx<br/>Payment Terms"]:::aspnetPage
                end
                
                MASTERS_CONFIG["Masters/web.config<br/>Role: Sales_Master"]:::aspnetPage
            end
            
            %% Transactions Section
            subgraph TRANSACTIONS["💼 Transactions Section"]
                direction TB
                TRANS_DASH["Transactions/Dashboard.aspx<br/>Transaction Menu"]:::aspnetPage
                
                %% Enquiry Management
                subgraph ENQUIRY_MGMT["📞 Enquiry Management"]
                    ENQUIRY_NEW["Enquiry_New.aspx<br/>Create Enquiry"]:::aspnetPage
                    ENQUIRY_EDIT["Enquiry_Edit.aspx<br/>Edit Enquiry"]:::aspnetPage
                    ENQUIRY_DELETE["Enquiry_Delete.aspx<br/>Delete Enquiry"]:::aspnetPage
                    ENQUIRY_FOLLOWUP["Enquiry_Followup.aspx<br/>Enquiry Follow-up"]:::aspnetPage
                    ENQUIRY_CONVERSION["Enquiry_Conversion.aspx<br/>Enquiry Conversion"]:::aspnetPage
                end
                
                %% Quotation Management
                subgraph QUOTATION_MGMT["📋 Quotation Management"]
                    QUOTATION_NEW["Quotation_New.aspx<br/>Create Quotation"]:::aspnetPage
                    QUOTATION_EDIT["Quotation_Edit.aspx<br/>Edit Quotation"]:::aspnetPage
                    QUOTATION_DELETE["Quotation_Delete.aspx<br/>Delete Quotation"]:::aspnetPage
                    QUOTATION_APPROVAL["Quotation_Approval.aspx<br/>Quotation Approval"]:::aspnetPage
                    QUOTATION_REVISION["Quotation_Revision.aspx<br/>Quotation Revision"]:::aspnetPage
                    QUOTATION_PRINT["Quotation_Print.aspx<br/>Print Quotation"]:::aspnetPage
                end
                
                %% Sales Order Management
                subgraph SALES_ORDER_MGMT["📝 Sales Order Management"]
                    SALES_ORDER_NEW["Sales_Order_New.aspx<br/>Create Sales Order"]:::aspnetPage
                    SALES_ORDER_EDIT["Sales_Order_Edit.aspx<br/>Edit Sales Order"]:::aspnetPage
                    SALES_ORDER_DELETE["Sales_Order_Delete.aspx<br/>Delete Sales Order"]:::aspnetPage
                    SALES_ORDER_APPROVAL["Sales_Order_Approval.aspx<br/>Sales Order Approval"]:::aspnetPage
                    SALES_ORDER_MODIFICATION["Sales_Order_Modification.aspx<br/>Order Modification"]:::aspnetPage
                    SALES_ORDER_PRINT["Sales_Order_Print.aspx<br/>Print Sales Order"]:::aspnetPage
                end
                
                %% Work Order Management
                subgraph WORK_ORDER_MGMT["🔧 Work Order Management"]
                    WORK_ORDER_NEW["Work_Order_New.aspx<br/>Create Work Order"]:::aspnetPage
                    WORK_ORDER_EDIT["Work_Order_Edit.aspx<br/>Edit Work Order"]:::aspnetPage
                    WORK_ORDER_DELETE["Work_Order_Delete.aspx<br/>Delete Work Order"]:::aspnetPage
                    WORK_ORDER_SCHEDULING["Work_Order_Scheduling.aspx<br/>WO Scheduling"]:::aspnetPage
                    WORK_ORDER_TRACKING["Work_Order_Tracking.aspx<br/>WO Tracking"]:::aspnetPage
                    WORK_ORDER_COMPLETION["Work_Order_Completion.aspx<br/>WO Completion"]:::aspnetPage
                end
                
                %% Delivery Management
                subgraph DELIVERY_MGMT["🚚 Delivery Management"]
                    DELIVERY_CHALLAN_NEW["Delivery_Challan_New.aspx<br/>Create Delivery Challan"]:::aspnetPage
                    DELIVERY_CHALLAN_EDIT["Delivery_Challan_Edit.aspx<br/>Edit Delivery Challan"]:::aspnetPage
                    DELIVERY_CHALLAN_DELETE["Delivery_Challan_Delete.aspx<br/>Delete Delivery Challan"]:::aspnetPage
                    DELIVERY_SCHEDULE["Delivery_Schedule.aspx<br/>Delivery Schedule"]:::aspnetPage
                    DELIVERY_TRACKING["Delivery_Tracking.aspx<br/>Delivery Tracking"]:::aspnetPage
                    DELIVERY_CONFIRMATION["Delivery_Confirmation.aspx<br/>Delivery Confirmation"]:::aspnetPage
                end
                
                %% Invoice Management
                subgraph INVOICE_MGMT["💰 Invoice Management"]
                    SALES_INVOICE_NEW["Sales_Invoice_New.aspx<br/>Create Sales Invoice"]:::aspnetPage
                    SALES_INVOICE_EDIT["Sales_Invoice_Edit.aspx<br/>Edit Sales Invoice"]:::aspnetPage
                    SALES_INVOICE_DELETE["Sales_Invoice_Delete.aspx<br/>Delete Sales Invoice"]:::aspnetPage
                    SALES_INVOICE_PRINT["Sales_Invoice_Print.aspx<br/>Print Sales Invoice"]:::aspnetPage
                    PROFORMA_INVOICE["Proforma_Invoice.aspx<br/>Proforma Invoice"]:::aspnetPage
                    TAX_INVOICE["Tax_Invoice.aspx<br/>Tax Invoice"]:::aspnetPage
                end
                
                %% Payment Management
                subgraph PAYMENT_MGMT["💳 Payment Management"]
                    PAYMENT_RECEIPT["Payment_Receipt.aspx<br/>Payment Receipt"]:::aspnetPage
                    PAYMENT_FOLLOWUP["Payment_Followup.aspx<br/>Payment Follow-up"]:::aspnetPage
                    CREDIT_NOTE_SALES["Credit_Note.aspx<br/>Credit Note"]:::aspnetPage
                    DEBIT_NOTE_SALES["Debit_Note.aspx<br/>Debit Note"]:::aspnetPage
                    OUTSTANDING_MANAGEMENT["Outstanding_Management.aspx<br/>Outstanding Management"]:::aspnetPage
                end
                
                %% Return Management
                subgraph RETURN_MGMT["🔄 Return Management"]
                    SALES_RETURN_NEW["Sales_Return_New.aspx<br/>Create Sales Return"]:::aspnetPage
                    SALES_RETURN_EDIT["Sales_Return_Edit.aspx<br/>Edit Sales Return"]:::aspnetPage
                    SALES_RETURN_DELETE["Sales_Return_Delete.aspx<br/>Delete Sales Return"]:::aspnetPage
                    SALES_RETURN_APPROVAL["Sales_Return_Approval.aspx<br/>Return Approval"]:::aspnetPage
                    REPLACEMENT_ORDER["Replacement_Order.aspx<br/>Replacement Order"]:::aspnetPage
                end
            end
            
            %% Reports Section
            subgraph REPORTS["📊 Reports Section"]
                direction TB
                REP_DASH["Reports/Dashboard.aspx<br/>Reports Menu"]:::aspnetPage
                
                %% Customer Reports
                subgraph CUSTOMER_REPORTS["👥 Customer Reports"]
                    CUSTOMER_LIST_REPORT["Customer_List_Report.aspx<br/>Customer List"]:::aspnetPage
                    CUSTOMER_LEDGER["Customer_Ledger.aspx<br/>Customer Ledger"]:::aspnetPage
                    CUSTOMER_AGING["Customer_Aging_Report.aspx<br/>Customer Aging"]:::aspnetPage
                    CUSTOMER_OUTSTANDING["Customer_Outstanding.aspx<br/>Outstanding Report"]:::aspnetPage
                end
                
                %% Sales Reports
                subgraph SALES_REPORTS["📈 Sales Reports"]
                    SALES_REGISTER["Sales_Register.aspx<br/>Sales Register"]:::aspnetPage
                    SALES_SUMMARY["Sales_Summary.aspx<br/>Sales Summary"]:::aspnetPage
                    SALES_ANALYSIS["Sales_Analysis.aspx<br/>Sales Analysis"]:::aspnetPage
                    PRODUCT_WISE_SALES["Product_Wise_Sales.aspx<br/>Product-wise Sales"]:::aspnetPage
                    TERRITORY_WISE_SALES["Territory_Wise_Sales.aspx<br/>Territory-wise Sales"]:::aspnetPage
                    SALESPERSON_PERFORMANCE["Salesperson_Performance.aspx<br/>Salesperson Performance"]:::aspnetPage
                end
                
                %% Order Reports
                subgraph ORDER_REPORTS["📝 Order Reports"]
                    ORDER_BOOK_REPORT["Order_Book_Report.aspx<br/>Order Book"]:::aspnetPage
                    PENDING_ORDERS["Pending_Orders.aspx<br/>Pending Orders"]:::aspnetPage
                    ORDER_EXECUTION_STATUS["Order_Execution_Status.aspx<br/>Execution Status"]:::aspnetPage
                    DELIVERY_SCHEDULE_REPORT["Delivery_Schedule_Report.aspx<br/>Delivery Schedule"]:::aspnetPage
                end
                
                %% Crystal Reports
                subgraph CRYSTAL_REPORTS["💎 Crystal Reports"]
                    CUSTOMER_LIST_RPT["Customer_List.rpt<br/>Customer List Report"]:::report
                    SALES_INVOICE_RPT["Sales_Invoice.rpt<br/>Sales Invoice Report"]:::report
                    QUOTATION_RPT["Quotation.rpt<br/>Quotation Report"]:::report
                    SALES_ORDER_RPT["Sales_Order.rpt<br/>Sales Order Report"]:::report
                    DELIVERY_CHALLAN_RPT["Delivery_Challan.rpt<br/>Delivery Challan Report"]:::report
                    SALES_REGISTER_RPT["Sales_Register.rpt<br/>Sales Register Report"]:::report
                end
            end
        end
        
        %% Django Cortex Structure
        subgraph DJANGO_CORTEX["🐍 Django Cortex Structure"]
            direction TB
            
            %% Django Dashboard
            DJANGO_DASH["SalesDashboardView<br/>Main Dashboard"]:::djangoView
            
            %% Customer Management
            subgraph DJANGO_CUSTOMER["👥 Customer Management"]
                CUSTOMER_LIST["CustomerListView<br/>Customer List"]:::djangoView
                CUSTOMER_CREATE["CustomerCreateView<br/>Create Customer"]:::djangoView
                CUSTOMER_UPDATE["CustomerUpdateView<br/>Update Customer"]:::djangoView
                CUSTOMER_DELETE["CustomerDeleteView<br/>Delete Customer"]:::djangoView
                CUSTOMER_DETAIL["CustomerDetailView<br/>Customer Details"]:::djangoView
            end
            
            %% Enquiry Management
            subgraph DJANGO_ENQUIRY["📞 Enquiry Management"]
                ENQUIRY_LIST["EnquiryListView<br/>Enquiry List"]:::djangoView
                ENQUIRY_CREATE["EnquiryCreateView<br/>Create Enquiry"]:::djangoView
                ENQUIRY_UPDATE["EnquiryUpdateView<br/>Update Enquiry"]:::djangoView
                ENQUIRY_DELETE["EnquiryDeleteView<br/>Delete Enquiry"]:::djangoView
                ENQUIRY_FOLLOWUP_VIEW["EnquiryFollowupView<br/>Follow-up Management"]:::djangoView
            end
            
            %% Quotation Management
            subgraph DJANGO_QUOTATION["📋 Quotation Management"]
                QUOTATION_LIST["QuotationListView<br/>Quotation List"]:::djangoView
                QUOTATION_CREATE["QuotationCreateView<br/>Create Quotation"]:::djangoView
                QUOTATION_UPDATE["QuotationUpdateView<br/>Update Quotation"]:::djangoView
                QUOTATION_DELETE["QuotationDeleteView<br/>Delete Quotation"]:::djangoView
                QUOTATION_APPROVAL_VIEW["QuotationApprovalView<br/>Quotation Approval"]:::djangoView
            end
            
            %% Sales Order Management
            subgraph DJANGO_SALES_ORDER["📝 Sales Order Management"]
                SALES_ORDER_LIST["SalesOrderListView<br/>Sales Order List"]:::djangoView
                SALES_ORDER_CREATE["SalesOrderCreateView<br/>Create Sales Order"]:::djangoView
                SALES_ORDER_UPDATE["SalesOrderUpdateView<br/>Update Sales Order"]:::djangoView
                SALES_ORDER_DELETE["SalesOrderDeleteView<br/>Delete Sales Order"]:::djangoView
                SALES_ORDER_APPROVAL_VIEW["SalesOrderApprovalView<br/>Order Approval"]:::djangoView
            end
            
            %% Work Order Management
            subgraph DJANGO_WORK_ORDER["🔧 Work Order Management"]
                WORK_ORDER_LIST["WorkOrderListView<br/>Work Order List"]:::djangoView
                WORK_ORDER_CREATE["WorkOrderCreateView<br/>Create Work Order"]:::djangoView
                WORK_ORDER_UPDATE["WorkOrderUpdateView<br/>Update Work Order"]:::djangoView
                WORK_ORDER_DELETE["WorkOrderDeleteView<br/>Delete Work Order"]:::djangoView
                WORK_ORDER_TRACKING_VIEW["WorkOrderTrackingView<br/>WO Tracking"]:::djangoView
            end
            
            %% Reporting
            subgraph DJANGO_REPORTING["📊 Sales Reporting"]
                SALES_REPORTS_DASH["SalesReportsDashboardView<br/>Reports Dashboard"]:::djangoView
                CUSTOMER_REPORT_VIEW["CustomerReportView<br/>Customer Reports"]:::djangoView
                SALES_ANALYSIS_VIEW["SalesAnalysisView<br/>Sales Analysis"]:::djangoView
                ORDER_REPORT_VIEW["OrderReportView<br/>Order Reports"]:::djangoView
                PERFORMANCE_REPORT_VIEW["PerformanceReportView<br/>Performance Reports"]:::djangoView
            end
        end
